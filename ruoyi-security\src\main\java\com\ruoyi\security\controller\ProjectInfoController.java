package com.ruoyi.security.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.security.domain.ProjectInfo;
import com.ruoyi.security.service.IProjectInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 项目信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/security/project")
public class ProjectInfoController extends BaseController
{
    @Autowired
    private IProjectInfoService projectInfoService;

    /**
     * 查询项目信息列表
     */
    @PreAuthorize("@ss.hasPermi('security:project:list')")
    @PostMapping("/list")
    public TableDataInfo list(ProjectInfo projectInfo)
    {
        startPage();
        List<ProjectInfo> list = projectInfoService.selectProjectInfoList(projectInfo);
        return getDataTable(list);
    }

    /**
     * 导出项目信息列表
     */
    @PreAuthorize("@ss.hasPermi('security:project:export')")
    @Log(title = "项目信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectInfo projectInfo)
    {
        List<ProjectInfo> list = projectInfoService.selectProjectInfoList(projectInfo);
        ExcelUtil<ProjectInfo> util = new ExcelUtil<ProjectInfo>(ProjectInfo.class);
        util.exportExcel(response, list, "项目信息数据");
    }

    /**
     * 获取项目信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('security:project:query')")
    @GetMapping(value = "/{projectId}")
    public AjaxResult getInfo(@PathVariable("projectId") Long projectId)
    {
        return success(projectInfoService.selectProjectInfoByProjectId(projectId));
    }

    /**
     * 新增项目信息
     */
    @PreAuthorize("@ss.hasPermi('security:project:add')")
    @Log(title = "项目信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProjectInfo projectInfo)
    {
        if (!projectInfoService.checkProjectCodeUnique(projectInfo))
        {
            return error("新增项目'" + projectInfo.getProjectName() + "'失败，项目编码已存在");
        }
        return toAjax(projectInfoService.insertProjectInfo(projectInfo));
    }

    /**
     * 修改项目信息
     */
    @PreAuthorize("@ss.hasPermi('security:project:edit')")
    @Log(title = "项目信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProjectInfo projectInfo)
    {
        if (!projectInfoService.checkProjectCodeUnique(projectInfo))
        {
            return error("修改项目'" + projectInfo.getProjectName() + "'失败，项目编码已存在");
        }
        return toAjax(projectInfoService.updateProjectInfo(projectInfo));
    }

    /**
     * 删除项目信息
     */
    @PreAuthorize("@ss.hasPermi('security:project:remove')")
    @Log(title = "项目信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{projectIds}")
    public AjaxResult remove(@PathVariable Long[] projectIds)
    {
        return toAjax(projectInfoService.deleteProjectInfoByProjectIds(projectIds));
    }

    /**
     * 测试Git连接
     */
    @PreAuthorize("@ss.hasPermi('security:project:test')")
    @PostMapping("/testConnection")
    public AjaxResult testConnection(@RequestBody ProjectInfo projectInfo)
    {
        boolean result = projectInfoService.testGitConnection(projectInfo);
        if (result)
        {
            return success("Git连接测试成功");
        }
        else
        {
            return error("Git连接测试失败，请检查Git地址和认证信息");
        }
    }

    /**
     * 校验项目编码
     */
    @GetMapping("/checkProjectCodeUnique")
    public AjaxResult checkProjectCodeUnique(ProjectInfo projectInfo)
    {
        return success(projectInfoService.checkProjectCodeUnique(projectInfo));
    }
}
