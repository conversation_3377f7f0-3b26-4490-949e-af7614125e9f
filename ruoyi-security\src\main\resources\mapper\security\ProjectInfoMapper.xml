<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.security.mapper.ProjectInfoMapper">
    
    <resultMap type="ProjectInfo" id="ProjectInfoResult">
        <result property="projectId"       column="project_id"       />
        <result property="projectName"     column="project_name"     />
        <result property="projectCode"     column="project_code"     />
        <result property="gitUrl"          column="git_url"          />
        <result property="gitBranch"       column="git_branch"       />
        <result property="gitUsername"     column="git_username"     />
        <result property="gitPassword"     column="git_password"     />
        <result property="projectDesc"     column="project_desc"     />
        <result property="projectOwner"    column="project_owner"    />
        <result property="scanStatus"      column="scan_status"      />
        <result property="lastScanTime"    column="last_scan_time"   />
        <result property="delFlag"         column="del_flag"         />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
        <result property="remark"          column="remark"           />
    </resultMap>

    <sql id="selectProjectInfoVo">
        select project_id, project_name, project_code, git_url, git_branch, git_username, git_password, project_desc, project_owner, scan_status, last_scan_time, del_flag, create_by, create_time, update_by, update_time, remark from project_info
    </sql>

    <select id="selectProjectInfoList" parameterType="ProjectInfo" resultMap="ProjectInfoResult">
        <include refid="selectProjectInfoVo"/>
        <where>  
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="projectCode != null  and projectCode != ''"> and project_code = #{projectCode}</if>
            <if test="gitUrl != null  and gitUrl != ''"> and git_url = #{gitUrl}</if>
            <if test="gitBranch != null  and gitBranch != ''"> and git_branch = #{gitBranch}</if>
            <if test="gitUsername != null  and gitUsername != ''"> and git_username = #{gitUsername}</if>
            <if test="projectOwner != null  and projectOwner != ''"> and project_owner = #{projectOwner}</if>
            <if test="scanStatus != null  and scanStatus != ''"> and scan_status = #{scanStatus}</if>
            <if test="lastScanTime != null "> and last_scan_time = #{lastScanTime}</if>
            and del_flag = '0'
        </where>
    </select>
    
    <select id="selectProjectInfoByProjectId" parameterType="Long" resultMap="ProjectInfoResult">
        <include refid="selectProjectInfoVo"/>
        where project_id = #{projectId}
    </select>
        
    <insert id="insertProjectInfo" parameterType="ProjectInfo" useGeneratedKeys="true" keyProperty="projectId">
        insert into project_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="projectCode != null and projectCode != ''">project_code,</if>
            <if test="gitUrl != null and gitUrl != ''">git_url,</if>
            <if test="gitBranch != null">git_branch,</if>
            <if test="gitUsername != null">git_username,</if>
            <if test="gitPassword != null">git_password,</if>
            <if test="projectDesc != null">project_desc,</if>
            <if test="projectOwner != null">project_owner,</if>
            <if test="scanStatus != null">scan_status,</if>
            <if test="lastScanTime != null">last_scan_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="projectCode != null and projectCode != ''">#{projectCode},</if>
            <if test="gitUrl != null and gitUrl != ''">#{gitUrl},</if>
            <if test="gitBranch != null">#{gitBranch},</if>
            <if test="gitUsername != null">#{gitUsername},</if>
            <if test="gitPassword != null">#{gitPassword},</if>
            <if test="projectDesc != null">#{projectDesc},</if>
            <if test="projectOwner != null">#{projectOwner},</if>
            <if test="scanStatus != null">#{scanStatus},</if>
            <if test="lastScanTime != null">#{lastScanTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateProjectInfo" parameterType="ProjectInfo">
        update project_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="projectCode != null and projectCode != ''">project_code = #{projectCode},</if>
            <if test="gitUrl != null and gitUrl != ''">git_url = #{gitUrl},</if>
            <if test="gitBranch != null">git_branch = #{gitBranch},</if>
            <if test="gitUsername != null">git_username = #{gitUsername},</if>
            <if test="gitPassword != null">git_password = #{gitPassword},</if>
            <if test="projectDesc != null">project_desc = #{projectDesc},</if>
            <if test="projectOwner != null">project_owner = #{projectOwner},</if>
            <if test="scanStatus != null">scan_status = #{scanStatus},</if>
            <if test="lastScanTime != null">last_scan_time = #{lastScanTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where project_id = #{projectId}
    </update>

    <delete id="deleteProjectInfoByProjectId" parameterType="Long">
        update project_info set del_flag = '2' where project_id = #{projectId}
    </delete>

    <delete id="deleteProjectInfoByProjectIds" parameterType="String">
        update project_info set del_flag = '2' where project_id in 
        <foreach item="projectId" collection="array" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </delete>

    <select id="checkProjectCodeUnique" parameterType="String" resultMap="ProjectInfoResult">
        <include refid="selectProjectInfoVo"/>
        where project_code = #{projectCode} and del_flag = '0' limit 1
    </select>

</mapper>
