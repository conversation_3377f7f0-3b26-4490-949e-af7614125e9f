package com.ruoyi.security.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * CVE漏洞信息对象 cve_info
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class CveInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** CVE编号 */
    private String cveId;

    /** CVE描述 */
    @Excel(name = "CVE描述")
    private String cveDesc;

    /** CVSS评分 */
    @Excel(name = "CVSS评分")
    private BigDecimal cvssScore;

    /** 严重等级(1低危 2中危 3高危 4严重) */
    @Excel(name = "严重等级", readConverterExp = "1=低危,2=中危,3=高危,4=严重")
    private String severityLevel;

    /** 发布日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date publishDate;

    /** 更新日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 影响组件(JSON格式) */
    private String affectedComponents;

    /** 参考链接(JSON格式) */
    private String references;

    /** 状态(0正常 1停用) */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setCveId(String cveId) 
    {
        this.cveId = cveId;
    }

    public String getCveId() 
    {
        return cveId;
    }
    public void setCveDesc(String cveDesc) 
    {
        this.cveDesc = cveDesc;
    }

    public String getCveDesc() 
    {
        return cveDesc;
    }
    public void setCvssScore(BigDecimal cvssScore) 
    {
        this.cvssScore = cvssScore;
    }

    public BigDecimal getCvssScore() 
    {
        return cvssScore;
    }
    public void setSeverityLevel(String severityLevel) 
    {
        this.severityLevel = severityLevel;
    }

    public String getSeverityLevel() 
    {
        return severityLevel;
    }
    public void setPublishDate(Date publishDate) 
    {
        this.publishDate = publishDate;
    }

    public Date getPublishDate() 
    {
        return publishDate;
    }
    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }
    public void setAffectedComponents(String affectedComponents) 
    {
        this.affectedComponents = affectedComponents;
    }

    public String getAffectedComponents() 
    {
        return affectedComponents;
    }
    public void setReferences(String references) 
    {
        this.references = references;
    }

    public String getReferences() 
    {
        return references;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    // 别名方法，用于兼容性
    public void setDescription(String description)
    {
        this.cveDesc = description;
    }

    public String getDescription()
    {
        return cveDesc;
    }

    public void setPublishedDate(Date publishedDate)
    {
        this.publishDate = publishedDate;
    }

    public Date getPublishedDate()
    {
        return publishDate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("cveId", getCveId())
            .append("cveDesc", getCveDesc())
            .append("cvssScore", getCvssScore())
            .append("severityLevel", getSeverityLevel())
            .append("publishDate", getPublishDate())
            .append("updateDate", getUpdateDate())
            .append("affectedComponents", getAffectedComponents())
            .append("references", getReferences())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
