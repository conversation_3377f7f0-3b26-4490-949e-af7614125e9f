<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('报告详情')" />
    <th:block th:include="include :: layout-latest-css" />
    <style>
        .report-header {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .risk-card {
            text-align: center;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .risk-high { background: #f2dede; border-left: 4px solid #d9534f; }
        .risk-medium { background: #fcf8e3; border-left: 4px solid #f0ad4e; }
        .risk-low { background: #d9edf7; border-left: 4px solid #5bc0de; }
        .risk-safe { background: #dff0d8; border-left: 4px solid #5cb85c; }
        .summary-stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #337ab7;
        }
        .vulnerability-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .severity-critical { border-left: 4px solid #d9534f; }
        .severity-high { border-left: 4px solid #f0ad4e; }
        .severity-medium { border-left: 4px solid #5bc0de; }
        .severity-low { border-left: 4px solid #5cb85c; }
    </style>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <!-- 报告头部信息 -->
        <div class="report-header" th:object="${report}">
            <div class="row">
                <div class="col-md-8">
                    <h2 th:text="*{reportTitle}">扫描报告标题</h2>
                    <p><strong>项目名称：</strong><span th:text="*{projectName}">项目名称</span></p>
                    <p><strong>扫描时间：</strong><span th:text="${#dates.format(report.generateTime, 'yyyy-MM-dd HH:mm:ss')}">扫描时间</span></p>
                </div>
                <div class="col-md-4">
                    <div class="risk-card" th:classappend="${report.riskLevel == 'HIGH'} ? 'risk-high' : (${report.riskLevel == 'MEDIUM'} ? 'risk-medium' : (${report.riskLevel == 'LOW'} ? 'risk-low' : 'risk-safe'))">
                        <h3>整体风险等级</h3>
                        <h2 th:text="${report.riskLevel == 'HIGH'} ? '高风险' : (${report.riskLevel == 'MEDIUM'} ? '中风险' : (${report.riskLevel == 'LOW'} ? '低风险' : '安全'))">风险等级</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="ibox">
            <div class="ibox-title">
                <h5>扫描统计概览</h5>
            </div>
            <div class="ibox-content">
                <div class="summary-stats">
                    <div class="stat-item">
                        <div class="stat-number" th:text="${report.totalDependencies}">0</div>
                        <div>依赖总数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number text-danger" th:text="${report.cveCount}">0</div>
                        <div>CVE漏洞</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number text-warning" th:text="${report.highRiskLicenseCount}">0</div>
                        <div>高风险许可证</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number text-info" th:text="${vulnerabilities != null ? vulnerabilities.size() : 0}">0</div>
                        <div>漏洞详情</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CVE漏洞详情 -->
        <div class="ibox" th:if="${vulnerabilities != null and !vulnerabilities.empty}">
            <div class="ibox-title">
                <h5>CVE漏洞详情</h5>
            </div>
            <div class="ibox-content">
                <div th:each="vuln : ${vulnerabilities}" class="vulnerability-item" 
                     th:classappend="${vuln.severity == 'CRITICAL'} ? 'severity-critical' : (${vuln.severity == 'HIGH'} ? 'severity-high' : (${vuln.severity == 'MEDIUM'} ? 'severity-medium' : 'severity-low'))">
                    <div class="row">
                        <div class="col-md-8">
                            <h4 th:text="${vuln.cveId}">CVE-2023-0001</h4>
                            <p th:text="${vuln.description}">漏洞描述</p>
                            <p><strong>影响组件：</strong><span th:text="${vuln.component}">组件名称</span></p>
                        </div>
                        <div class="col-md-4 text-right">
                            <span class="label" 
                                  th:classappend="${vuln.severity == 'CRITICAL'} ? 'label-danger' : (${vuln.severity == 'HIGH'} ? 'label-warning' : (${vuln.severity == 'MEDIUM'} ? 'label-info' : 'label-default'))"
                                  th:text="${vuln.severity}">严重程度</span>
                            <br><br>
                            <p><strong>CVSS评分：</strong><span th:text="${vuln.cvssScore}">0.0</span></p>
                            <p><strong>发布时间：</strong><span th:text="${#dates.format(vuln.publishedDate, 'yyyy-MM-dd')}">发布时间</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 许可证风险详情 -->
        <div class="ibox" th:if="${licenseRisks != null and !licenseRisks.empty}">
            <div class="ibox-title">
                <h5>许可证风险详情</h5>
            </div>
            <div class="ibox-content">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>组件名称</th>
                                <th>许可证类型</th>
                                <th>风险等级</th>
                                <th>风险描述</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="license : ${licenseRisks}">
                                <td th:text="${license.component}">组件名称</td>
                                <td th:text="${license.licenseType}">许可证类型</td>
                                <td>
                                    <span class="label" 
                                          th:classappend="${license.riskLevel == 'HIGH'} ? 'label-danger' : (${license.riskLevel == 'MEDIUM'} ? 'label-warning' : 'label-info')"
                                          th:text="${license.riskLevel == 'HIGH'} ? '高风险' : (${license.riskLevel == 'MEDIUM'} ? '中风险' : '低风险')">风险等级</span>
                                </td>
                                <td th:text="${license.riskDescription}">风险描述</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 依赖列表 -->
        <div class="ibox" th:if="${dependencies != null and !dependencies.empty}">
            <div class="ibox-title">
                <h5>项目依赖列表</h5>
            </div>
            <div class="ibox-content">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>组件名称</th>
                                <th>版本</th>
                                <th>许可证</th>
                                <th>风险状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="dep : ${dependencies}">
                                <td th:text="${dep.groupId + ':' + dep.artifactId}">组件名称</td>
                                <td th:text="${dep.version}">版本</td>
                                <td th:text="${dep.license}">许可证</td>
                                <td>
                                    <span th:if="${dep.hasVulnerability}" class="label label-danger">存在漏洞</span>
                                    <span th:if="${dep.hasLicenseRisk}" class="label label-warning">许可证风险</span>
                                    <span th:if="${!dep.hasVulnerability and !dep.hasLicenseRisk}" class="label label-success">安全</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="row">
            <div class="col-sm-12 text-center">
                <a class="btn btn-success" th:href="@{'/security/report/download/' + ${report.reportId}}">
                    <i class="fa fa-download"></i> 下载完整报告
                </a>
                <a class="btn btn-default" href="javascript:history.back()">
                    <i class="fa fa-arrow-left"></i> 返回列表
                </a>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
</body>
</html>
