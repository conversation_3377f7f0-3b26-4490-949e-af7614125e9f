package com.ruoyi.security.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 安全扫描配置
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Configuration
@ConfigurationProperties(prefix = "ruoyi.security.scan")
public class SecurityScanConfig
{
    /**
     * 扫描工作目录
     */
    private String workspace = "/tmp/security-scan";

    /**
     * NVD API配置
     */
    private String nvdApiUrl = "https://services.nvd.nist.gov/rest/json/cves/2.0";

    /**
     * Maven中央仓库API配置
     */
    private String mavenCentralApiUrl = "https://search.maven.org/solrsearch/select";

    /**
     * 扫描超时时间（分钟）
     */
    private int scanTimeoutMinutes = 30;

    /**
     * 并发扫描任务数
     */
    private int maxConcurrentScans = 3;

    public String getWorkspace()
    {
        return workspace;
    }

    public void setWorkspace(String workspace)
    {
        this.workspace = workspace;
    }

    public String getNvdApiUrl()
    {
        return nvdApiUrl;
    }

    public void setNvdApiUrl(String nvdApiUrl)
    {
        this.nvdApiUrl = nvdApiUrl;
    }

    public String getMavenCentralApiUrl()
    {
        return mavenCentralApiUrl;
    }

    public void setMavenCentralApiUrl(String mavenCentralApiUrl)
    {
        this.mavenCentralApiUrl = mavenCentralApiUrl;
    }

    public int getScanTimeoutMinutes()
    {
        return scanTimeoutMinutes;
    }

    public void setScanTimeoutMinutes(int scanTimeoutMinutes)
    {
        this.scanTimeoutMinutes = scanTimeoutMinutes;
    }

    public int getMaxConcurrentScans()
    {
        return maxConcurrentScans;
    }

    public void setMaxConcurrentScans(int maxConcurrentScans)
    {
        this.maxConcurrentScans = maxConcurrentScans;
    }
}
