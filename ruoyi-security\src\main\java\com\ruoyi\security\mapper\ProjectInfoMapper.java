package com.ruoyi.security.mapper;

import java.util.List;
import com.ruoyi.security.domain.ProjectInfo;

/**
 * 项目信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface ProjectInfoMapper 
{
    /**
     * 查询项目信息
     * 
     * @param projectId 项目信息主键
     * @return 项目信息
     */
    public ProjectInfo selectProjectInfoByProjectId(Long projectId);

    /**
     * 查询项目信息列表
     * 
     * @param projectInfo 项目信息
     * @return 项目信息集合
     */
    public List<ProjectInfo> selectProjectInfoList(ProjectInfo projectInfo);

    /**
     * 新增项目信息
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    public int insertProjectInfo(ProjectInfo projectInfo);

    /**
     * 修改项目信息
     * 
     * @param projectInfo 项目信息
     * @return 结果
     */
    public int updateProjectInfo(ProjectInfo projectInfo);

    /**
     * 删除项目信息
     * 
     * @param projectId 项目信息主键
     * @return 结果
     */
    public int deleteProjectInfoByProjectId(Long projectId);

    /**
     * 批量删除项目信息
     * 
     * @param projectIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectInfoByProjectIds(Long[] projectIds);

    /**
     * 校验项目编码是否唯一
     * 
     * @param projectCode 项目编码
     * @return 结果
     */
    public ProjectInfo checkProjectCodeUnique(String projectCode);
}
