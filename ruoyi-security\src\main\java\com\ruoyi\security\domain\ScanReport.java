package com.ruoyi.security.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 扫描报告对象 scan_report
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class ScanReport extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 报告ID */
    private Long reportId;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Long projectId;

    /** 任务ID */
    @Excel(name = "任务ID")
    private Long taskId;

    /** 项目名称 */
    @Excel(name = "项目名称")
    private String projectName;

    /** 报告内容(JSON格式) */
    private String reportContent;

    /** 报告标题 */
    @Excel(name = "报告标题")
    private String reportTitle;

    /** 总依赖数 */
    @Excel(name = "总依赖数")
    private Long totalDependencies;

    /** CVE漏洞数 */
    @Excel(name = "CVE漏洞数")
    private Long cveCount;

    /** 高风险许可证数 */
    @Excel(name = "高风险许可证数")
    private Long highRiskLicenseCount;

    public void setReportId(Long reportId) 
    {
        this.reportId = reportId;
    }

    public Long getReportId() 
    {
        return reportId;
    }
    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }
    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }
    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    public String getProjectName() 
    {
        return projectName;
    }
    public void setReportContent(String reportContent) 
    {
        this.reportContent = reportContent;
    }

    public String getReportContent()
    {
        return reportContent;
    }

    public void setReportTitle(String reportTitle)
    {
        this.reportTitle = reportTitle;
    }

    public String getReportTitle()
    {
        return reportTitle;
    }

    public void setTotalDependencies(Long totalDependencies)
    {
        this.totalDependencies = totalDependencies;
    }

    public Long getTotalDependencies()
    {
        return totalDependencies;
    }

    public void setCveCount(Long cveCount)
    {
        this.cveCount = cveCount;
    }

    public Long getCveCount()
    {
        return cveCount;
    }

    public void setHighRiskLicenseCount(Long highRiskLicenseCount)
    {
        this.highRiskLicenseCount = highRiskLicenseCount;
    }

    public Long getHighRiskLicenseCount()
    {
        return highRiskLicenseCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("reportId", getReportId())
            .append("projectId", getProjectId())
            .append("taskId", getTaskId())
            .append("projectName", getProjectName())
            .append("reportContent", getReportContent())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}
