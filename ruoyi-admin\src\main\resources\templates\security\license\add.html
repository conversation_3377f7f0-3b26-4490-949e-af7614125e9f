<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增许可证')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-license-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">许可证名称：</label>
                <div class="col-sm-8">
                    <input name="licenseName" class="form-control" type="text" placeholder="请输入许可证名称" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">许可证类型：</label>
                <div class="col-sm-8">
                    <select name="licenseType" class="form-control">
                        <option value="">请选择许可证类型</option>
                        <option value="MIT">MIT License</option>
                        <option value="Apache-2.0">Apache License 2.0</option>
                        <option value="GPL-2.0">GNU General Public License v2.0</option>
                        <option value="GPL-3.0">GNU General Public License v3.0</option>
                        <option value="LGPL-2.1">GNU Lesser General Public License v2.1</option>
                        <option value="LGPL-3.0">GNU Lesser General Public License v3.0</option>
                        <option value="AGPL-3.0">GNU Affero General Public License v3.0</option>
                        <option value="BSD-2-Clause">BSD 2-Clause License</option>
                        <option value="BSD-3-Clause">BSD 3-Clause License</option>
                        <option value="ISC">ISC License</option>
                        <option value="MPL-2.0">Mozilla Public License 2.0</option>
                        <option value="CC0-1.0">Creative Commons Zero v1.0</option>
                        <option value="Unlicense">The Unlicense</option>
                        <option value="Other">其他</option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">风险等级：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" id="riskHigh" name="riskLevel" value="HIGH">
                        <label for="riskHigh">高风险</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" id="riskMedium" name="riskLevel" value="MEDIUM">
                        <label for="riskMedium">中风险</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" id="riskLow" name="riskLevel" value="LOW" checked>
                        <label for="riskLow">低风险</label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">风险描述：</label>
                <div class="col-sm-8">
                    <textarea name="riskDescription" class="form-control" rows="3" placeholder="请输入风险描述"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">商业使用：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" id="commercialYes" name="isCommercialUse" value="1" checked>
                        <label for="commercialYes">允许</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" id="commercialNo" name="isCommercialUse" value="0">
                        <label for="commercialNo">禁止</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" id="commercialUnknown" name="isCommercialUse" value="2">
                        <label for="commercialUnknown">未知</label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">修改权限：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" id="modifyYes" name="isModification" value="1" checked>
                        <label for="modifyYes">允许</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" id="modifyNo" name="isModification" value="0">
                        <label for="modifyNo">禁止</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" id="modifyUnknown" name="isModification" value="2">
                        <label for="modifyUnknown">未知</label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">分发权限：</label>
                <div class="col-sm-8">
                    <div class="radio-box">
                        <input type="radio" id="distributeYes" name="isDistribution" value="1" checked>
                        <label for="distributeYes">允许</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" id="distributeNo" name="isDistribution" value="0">
                        <label for="distributeNo">禁止</label>
                    </div>
                    <div class="radio-box">
                        <input type="radio" id="distributeUnknown" name="isDistribution" value="2">
                        <label for="distributeUnknown">未知</label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">许可证URL：</label>
                <div class="col-sm-8">
                    <input name="licenseUrl" class="form-control" type="url" placeholder="请输入许可证官方URL">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">许可证文本：</label>
                <div class="col-sm-8">
                    <textarea name="licenseText" class="form-control" rows="5" placeholder="请输入许可证完整文本（可选）"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
                        <input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <textarea name="remark" class="form-control" rows="3" placeholder="请输入备注信息"></textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "security/license";
        $("#form-license-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix, $('#form-license-add').serialize());
            }
        }

        // 根据许可证类型自动设置风险等级和描述
        $("select[name='licenseType']").change(function() {
            var licenseType = $(this).val();
            var riskLevel = 'LOW';
            var riskDescription = '';
            
            switch(licenseType) {
                case 'GPL-2.0':
                case 'GPL-3.0':
                case 'AGPL-3.0':
                    riskLevel = 'HIGH';
                    riskDescription = 'Copyleft许可证，要求衍生作品必须使用相同许可证开源';
                    $("input[name='isCommercialUse'][value='0']").prop('checked', true);
                    break;
                case 'LGPL-2.1':
                case 'LGPL-3.0':
                    riskLevel = 'MEDIUM';
                    riskDescription = '弱Copyleft许可证，动态链接时可商用，静态链接需开源';
                    break;
                case 'MPL-2.0':
                    riskLevel = 'MEDIUM';
                    riskDescription = '文件级Copyleft，修改的文件需开源，新增文件可闭源';
                    break;
                case 'MIT':
                case 'Apache-2.0':
                case 'BSD-2-Clause':
                case 'BSD-3-Clause':
                case 'ISC':
                    riskLevel = 'LOW';
                    riskDescription = '宽松许可证，允许商业使用和闭源';
                    break;
            }
            
            $("input[name='riskLevel'][value='" + riskLevel + "']").prop('checked', true);
            $("textarea[name='riskDescription']").val(riskDescription);
        });
    </script>
</body>
</html>
