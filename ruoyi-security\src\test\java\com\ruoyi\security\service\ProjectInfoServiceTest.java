package com.ruoyi.security.service;

import com.ruoyi.security.domain.ProjectInfo;
import com.ruoyi.security.service.IProjectInfoService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 项目信息服务测试
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class ProjectInfoServiceTest {

    @Autowired
    private IProjectInfoService projectInfoService;

    private ProjectInfo testProject;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testProject = new ProjectInfo();
        testProject.setProjectName("测试项目");
        testProject.setProjectCode("TEST001");
        testProject.setGitUrl("https://gitlab.com/test/project.git");
        testProject.setGitBranch("master");
        testProject.setGitUsername("testuser");
        testProject.setGitPassword("testpass");
        testProject.setProjectDesc("这是一个测试项目");
        testProject.setProjectOwner("admin");
        testProject.setStatus("0");
    }

    @Test
    @DisplayName("测试新增项目")
    void testInsertProjectInfo() {
        // 执行新增
        int result = projectInfoService.insertProjectInfo(testProject);
        
        // 验证结果
        assertEquals(1, result, "应该成功插入一条记录");
        assertNotNull(testProject.getProjectId(), "项目ID应该自动生成");
        assertNotNull(testProject.getCreateTime(), "创建时间应该自动设置");
    }

    @Test
    @DisplayName("测试查询项目列表")
    void testSelectProjectInfoList() {
        // 先插入测试数据
        projectInfoService.insertProjectInfo(testProject);
        
        // 查询列表
        ProjectInfo queryParam = new ProjectInfo();
        queryParam.setProjectName("测试");
        List<ProjectInfo> list = projectInfoService.selectProjectInfoList(queryParam);
        
        // 验证结果
        assertFalse(list.isEmpty(), "应该能查询到项目列表");
        assertTrue(list.stream().anyMatch(p -> "TEST001".equals(p.getProjectCode())), 
                  "列表中应该包含测试项目");
    }

    @Test
    @DisplayName("测试根据ID查询项目")
    void testSelectProjectInfoById() {
        // 先插入测试数据
        projectInfoService.insertProjectInfo(testProject);
        Long projectId = testProject.getProjectId();
        
        // 根据ID查询
        ProjectInfo result = projectInfoService.selectProjectInfoByProjectId(projectId);
        
        // 验证结果
        assertNotNull(result, "应该能查询到项目");
        assertEquals("TEST001", result.getProjectCode(), "项目编码应该匹配");
        assertEquals("测试项目", result.getProjectName(), "项目名称应该匹配");
    }

    @Test
    @DisplayName("测试更新项目")
    void testUpdateProjectInfo() {
        // 先插入测试数据
        projectInfoService.insertProjectInfo(testProject);
        
        // 修改项目信息
        testProject.setProjectName("修改后的项目名称");
        testProject.setProjectDesc("修改后的项目描述");
        testProject.setScanStatus("1");
        
        // 执行更新
        int result = projectInfoService.updateProjectInfo(testProject);
        
        // 验证结果
        assertEquals(1, result, "应该成功更新一条记录");
        
        // 查询验证
        ProjectInfo updated = projectInfoService.selectProjectInfoByProjectId(testProject.getProjectId());
        assertEquals("修改后的项目名称", updated.getProjectName(), "项目名称应该已更新");
        assertEquals("1", updated.getScanStatus(), "扫描状态应该已更新");
        assertNotNull(updated.getUpdateTime(), "更新时间应该自动设置");
    }

    @Test
    @DisplayName("测试删除项目")
    void testDeleteProjectInfo() {
        // 先插入测试数据
        projectInfoService.insertProjectInfo(testProject);
        Long projectId = testProject.getProjectId();
        
        // 执行删除
        int result = projectInfoService.deleteProjectInfoByProjectId(projectId);
        
        // 验证结果
        assertEquals(1, result, "应该成功删除一条记录");
        
        // 查询验证（软删除）
        ProjectInfo deleted = projectInfoService.selectProjectInfoByProjectId(projectId);
        assertNull(deleted, "删除后应该查询不到项目");
    }

    @Test
    @DisplayName("测试批量删除项目")
    void testBatchDeleteProjectInfo() {
        // 插入多个测试项目
        projectInfoService.insertProjectInfo(testProject);
        
        ProjectInfo testProject2 = new ProjectInfo();
        testProject2.setProjectName("测试项目2");
        testProject2.setProjectCode("TEST002");
        testProject2.setGitUrl("https://gitlab.com/test/project2.git");
        projectInfoService.insertProjectInfo(testProject2);
        
        // 批量删除
        Long[] projectIds = {testProject.getProjectId(), testProject2.getProjectId()};
        int result = projectInfoService.deleteProjectInfoByProjectIds(projectIds);
        
        // 验证结果
        assertEquals(2, result, "应该成功删除两条记录");
    }

    @Test
    @DisplayName("测试项目编码唯一性校验")
    void testProjectCodeUniqueness() {
        // 先插入一个项目
        projectInfoService.insertProjectInfo(testProject);
        
        // 尝试插入相同编码的项目
        ProjectInfo duplicateProject = new ProjectInfo();
        duplicateProject.setProjectName("重复项目");
        duplicateProject.setProjectCode("TEST001"); // 相同编码
        duplicateProject.setGitUrl("https://gitlab.com/test/duplicate.git");
        
        // 应该抛出异常或返回失败
        assertThrows(Exception.class, () -> {
            projectInfoService.insertProjectInfo(duplicateProject);
        }, "重复的项目编码应该抛出异常");
    }

    @Test
    @DisplayName("测试项目编码校验")
    void testCheckProjectCodeUnique() {
        // 先插入测试数据
        projectInfoService.insertProjectInfo(testProject);
        
        // 测试已存在的编码
        ProjectInfo checkExisting = new ProjectInfo();
        checkExisting.setProjectCode("TEST001");
        String result1 = projectInfoService.checkProjectCodeUnique(checkExisting);
        assertEquals("1", result1, "已存在的编码应该返回不唯一");
        
        // 测试不存在的编码
        ProjectInfo checkNew = new ProjectInfo();
        checkNew.setProjectCode("TEST999");
        String result2 = projectInfoService.checkProjectCodeUnique(checkNew);
        assertEquals("0", result2, "不存在的编码应该返回唯一");
        
        // 测试更新时的编码校验
        ProjectInfo checkUpdate = new ProjectInfo();
        checkUpdate.setProjectId(testProject.getProjectId());
        checkUpdate.setProjectCode("TEST001");
        String result3 = projectInfoService.checkProjectCodeUnique(checkUpdate);
        assertEquals("0", result3, "更新自己的编码应该返回唯一");
    }

    @Test
    @DisplayName("测试Git连接测试")
    void testGitConnectionTest() {
        // 测试有效的Git配置
        ProjectInfo validProject = new ProjectInfo();
        validProject.setGitUrl("https://github.com/octocat/Hello-World.git");
        validProject.setGitBranch("master");
        
        // 注意：这个测试需要网络连接，在实际环境中可能需要Mock
        boolean result1 = projectInfoService.testGitConnection(validProject);
        // 由于网络环境不确定，这里只验证方法能正常执行
        assertNotNull(result1, "Git连接测试应该返回结果");
        
        // 测试无效的Git配置
        ProjectInfo invalidProject = new ProjectInfo();
        invalidProject.setGitUrl("https://invalid-git-url.com/invalid/repo.git");
        invalidProject.setGitBranch("master");
        
        boolean result2 = projectInfoService.testGitConnection(invalidProject);
        assertFalse(result2, "无效的Git配置应该返回false");
    }

    @Test
    @DisplayName("测试参数验证")
    void testParameterValidation() {
        // 测试空项目名称
        ProjectInfo emptyNameProject = new ProjectInfo();
        emptyNameProject.setProjectCode("TEST003");
        emptyNameProject.setGitUrl("https://gitlab.com/test/project.git");
        
        assertThrows(Exception.class, () -> {
            projectInfoService.insertProjectInfo(emptyNameProject);
        }, "空项目名称应该抛出异常");
        
        // 测试空项目编码
        ProjectInfo emptyCodeProject = new ProjectInfo();
        emptyCodeProject.setProjectName("测试项目");
        emptyCodeProject.setGitUrl("https://gitlab.com/test/project.git");
        
        assertThrows(Exception.class, () -> {
            projectInfoService.insertProjectInfo(emptyCodeProject);
        }, "空项目编码应该抛出异常");
        
        // 测试空Git地址
        ProjectInfo emptyGitProject = new ProjectInfo();
        emptyGitProject.setProjectName("测试项目");
        emptyGitProject.setProjectCode("TEST004");
        
        assertThrows(Exception.class, () -> {
            projectInfoService.insertProjectInfo(emptyGitProject);
        }, "空Git地址应该抛出异常");
    }

    @Test
    @DisplayName("测试扫描状态更新")
    void testScanStatusUpdate() {
        // 先插入测试数据
        projectInfoService.insertProjectInfo(testProject);
        
        // 更新扫描状态为扫描中
        testProject.setScanStatus("1");
        projectInfoService.updateProjectInfo(testProject);
        
        // 验证状态更新
        ProjectInfo updated = projectInfoService.selectProjectInfoByProjectId(testProject.getProjectId());
        assertEquals("1", updated.getScanStatus(), "扫描状态应该更新为扫描中");
        
        // 更新扫描状态为已完成
        testProject.setScanStatus("2");
        testProject.setLastScanTime(new java.util.Date());
        projectInfoService.updateProjectInfo(testProject);
        
        // 验证状态更新
        updated = projectInfoService.selectProjectInfoByProjectId(testProject.getProjectId());
        assertEquals("2", updated.getScanStatus(), "扫描状态应该更新为已完成");
        assertNotNull(updated.getLastScanTime(), "最后扫描时间应该已设置");
    }

    @Test
    @DisplayName("测试模糊查询")
    void testFuzzyQuery() {
        // 插入多个测试项目
        projectInfoService.insertProjectInfo(testProject);
        
        ProjectInfo testProject2 = new ProjectInfo();
        testProject2.setProjectName("另一个测试项目");
        testProject2.setProjectCode("TEST002");
        testProject2.setGitUrl("https://gitlab.com/test/project2.git");
        projectInfoService.insertProjectInfo(testProject2);
        
        ProjectInfo normalProject = new ProjectInfo();
        normalProject.setProjectName("正常项目");
        normalProject.setProjectCode("NORMAL001");
        normalProject.setGitUrl("https://gitlab.com/normal/project.git");
        projectInfoService.insertProjectInfo(normalProject);
        
        // 模糊查询包含"测试"的项目
        ProjectInfo queryParam = new ProjectInfo();
        queryParam.setProjectName("测试");
        List<ProjectInfo> testProjects = projectInfoService.selectProjectInfoList(queryParam);
        
        // 验证结果
        assertEquals(2, testProjects.size(), "应该查询到2个测试项目");
        assertTrue(testProjects.stream().allMatch(p -> p.getProjectName().contains("测试")), 
                  "所有结果都应该包含'测试'");
    }
}
