<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('许可证管理')" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="license-form">
                    <div class="select-list">
                        <ul>
                            <li>
                                许可证名称：<input type="text" name="licenseName"/>
                            </li>
                            <li>
                                风险等级：<select name="riskLevel">
                                    <option value="">所有</option>
                                    <option value="HIGH">高风险</option>
                                    <option value="MEDIUM">中风险</option>
                                    <option value="LOW">低风险</option>
                                </select>
                            </li>
                            <li>
                                状态：<select name="status" th:with="type=${@dict.getType('sys_normal_disable')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="security:license:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="security:license:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="security:license:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="security:license:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('security:license:edit')}]];
        var removeFlag = [[${@permission.hasPermi('security:license:remove')}]];
        var prefix = ctx + "security/license";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "许可证",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'licenseId',
                    title: '许可证ID',
                    visible: false
                },
                {
                    field: 'licenseName',
                    title: '许可证名称'
                },
                {
                    field: 'licenseType',
                    title: '许可证类型'
                },
                {
                    field: 'riskLevel',
                    title: '风险等级',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var riskMap = {
                            'HIGH': '<span class="label label-danger">高风险</span>',
                            'MEDIUM': '<span class="label label-warning">中风险</span>',
                            'LOW': '<span class="label label-info">低风险</span>'
                        };
                        return riskMap[value] || '<span class="label label-default">' + value + '</span>';
                    }
                },
                {
                    field: 'riskDescription',
                    title: '风险描述',
                    formatter: function(value, row, index) {
                        if (value && value.length > 50) {
                            return value.substring(0, 50) + '...';
                        }
                        return value;
                    }
                },
                {
                    field: 'isCommercialUse',
                    title: '商业使用',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value === '1') {
                            return '<span class="label label-success">允许</span>';
                        } else if (value === '0') {
                            return '<span class="label label-danger">禁止</span>';
                        } else {
                            return '<span class="label label-warning">未知</span>';
                        }
                    }
                },
                {
                    field: 'isModification',
                    title: '修改权限',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value === '1') {
                            return '<span class="label label-success">允许</span>';
                        } else if (value === '0') {
                            return '<span class="label label-danger">禁止</span>';
                        } else {
                            return '<span class="label label-warning">未知</span>';
                        }
                    }
                },
                {
                    field: 'isDistribution',
                    title: '分发权限',
                    align: 'center',
                    formatter: function(value, row, index) {
                        if (value === '1') {
                            return '<span class="label label-success">允许</span>';
                        } else if (value === '0') {
                            return '<span class="label label-danger">禁止</span>';
                        } else {
                            return '<span class="label label-warning">未知</span>';
                        }
                    }
                },
                {
                    field: 'status',
                    title: '状态',
                    align: 'center',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel([[${@dict.getType('sys_normal_disable')}]], value);
                    }
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    sortable: true
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.licenseId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.licenseId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
