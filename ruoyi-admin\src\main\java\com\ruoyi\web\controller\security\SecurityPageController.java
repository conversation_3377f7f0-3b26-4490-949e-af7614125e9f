package com.ruoyi.web.controller.security;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import com.ruoyi.security.domain.ProjectInfo;
import com.ruoyi.security.domain.LicenseInfo;
import com.ruoyi.security.service.IProjectInfoService;
import com.ruoyi.security.service.ILicenseInfoService;

/**
 * 安全扫描页面Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Controller
@RequestMapping("/security")
public class SecurityPageController
{
    @Autowired
    private IProjectInfoService projectInfoService;
    
    @Autowired
    private ILicenseInfoService licenseInfoService;

    private String prefix = "security";

    /**
     * 项目管理页面
     */
    @PreAuthorize("@ss.hasPermi('security:project:view')")
    @GetMapping("/project")
    public String project()
    {
        return prefix + "/project/project";
    }

    /**
     * 新增项目页面
     */
    @PreAuthorize("@ss.hasPermi('security:project:add')")
    @GetMapping("/project/add")
    public String add()
    {
        return prefix + "/project/add";
    }

    /**
     * 修改项目页面
     */
    @PreAuthorize("@ss.hasPermi('security:project:edit')")
    @GetMapping("/project/edit/{projectId}")
    public String edit(@PathVariable("projectId") Long projectId, ModelMap mmap)
    {
        ProjectInfo projectInfo = projectInfoService.selectProjectInfoByProjectId(projectId);
        mmap.put("projectInfo", projectInfo);
        return prefix + "/project/edit";
    }

    /**
     * 扫描管理页面
     */
    @PreAuthorize("@ss.hasPermi('security:scan:view')")
    @GetMapping("/scan")
    public String scan()
    {
        return prefix + "/scan/scan";
    }

    /**
     * 选择项目页面
     */
    @PreAuthorize("@ss.hasPermi('security:scan:add')")
    @GetMapping("/scan/selectProject")
    public String selectProject()
    {
        return prefix + "/scan/selectProject";
    }

    /**
     * 扫描报告页面
     */
    @PreAuthorize("@ss.hasPermi('security:report:view')")
    @GetMapping("/report")
    public String report()
    {
        return prefix + "/report/report";
    }

    /**
     * 报告详情页面
     */
    @PreAuthorize("@ss.hasPermi('security:report:detail')")
    @GetMapping("/report/detail/{reportId}")
    public String detail(@PathVariable("reportId") Long reportId, ModelMap mmap)
    {
        mmap.put("reportId", reportId);
        return prefix + "/report/detail";
    }

    /**
     * CVE管理页面
     */
    @PreAuthorize("@ss.hasPermi('security:cve:view')")
    @GetMapping("/cve")
    public String cve()
    {
        return prefix + "/cve/cve";
    }

    /**
     * 许可证管理页面
     */
    @PreAuthorize("@ss.hasPermi('security:license:view')")
    @GetMapping("/license")
    public String license()
    {
        return prefix + "/license/license";
    }

    /**
     * 新增许可证页面
     */
    @PreAuthorize("@ss.hasPermi('security:license:add')")
    @GetMapping("/license/add")
    public String addLicense()
    {
        return prefix + "/license/add";
    }

    /**
     * 修改许可证页面
     */
    @PreAuthorize("@ss.hasPermi('security:license:edit')")
    @GetMapping("/license/edit/{licenseId}")
    public String editLicense(@PathVariable("licenseId") Long licenseId, ModelMap mmap)
    {
        LicenseInfo licenseInfo = licenseInfoService.selectLicenseInfoByLicenseId(licenseId);
        mmap.put("licenseInfo", licenseInfo);
        return prefix + "/license/edit";
    }

    /**
     * 安全仪表板页面
     */
    @PreAuthorize("@ss.hasPermi('security:dashboard:view')")
    @GetMapping("/dashboard")
    public String dashboard()
    {
        return prefix + "/dashboard/dashboard";
    }
}
