package com.ruoyi.security.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.security.mapper.CveInfoMapper;
import com.ruoyi.security.domain.CveInfo;
import com.ruoyi.security.service.ICveInfoService;

/**
 * CVE漏洞信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Service
public class CveInfoServiceImpl implements ICveInfoService 
{
    private static final Logger log = LoggerFactory.getLogger(CveInfoServiceImpl.class);

    @Autowired
    private CveInfoMapper cveInfoMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final CloseableHttpClient httpClient = HttpClients.createDefault();

    /**
     * 查询CVE漏洞信息
     * 
     * @param cveId CVE漏洞信息主键
     * @return CVE漏洞信息
     */
    @Override
    public CveInfo selectCveInfoByCveId(String cveId)
    {
        return cveInfoMapper.selectCveInfoByCveId(cveId);
    }

    /**
     * 查询CVE漏洞信息列表
     * 
     * @param cveInfo CVE漏洞信息
     * @return CVE漏洞信息
     */
    @Override
    public List<CveInfo> selectCveInfoList(CveInfo cveInfo)
    {
        return cveInfoMapper.selectCveInfoList(cveInfo);
    }

    /**
     * 新增CVE漏洞信息
     * 
     * @param cveInfo CVE漏洞信息
     * @return 结果
     */
    @Override
    public int insertCveInfo(CveInfo cveInfo)
    {
        cveInfo.setCreateTime(new Date());
        cveInfo.setCreateBy(SecurityUtils.getUsername());
        return cveInfoMapper.insertCveInfo(cveInfo);
    }

    /**
     * 修改CVE漏洞信息
     * 
     * @param cveInfo CVE漏洞信息
     * @return 结果
     */
    @Override
    public int updateCveInfo(CveInfo cveInfo)
    {
        cveInfo.setUpdateTime(new Date());
        cveInfo.setUpdateBy(SecurityUtils.getUsername());
        return cveInfoMapper.updateCveInfo(cveInfo);
    }

    /**
     * 批量删除CVE漏洞信息
     * 
     * @param cveIds 需要删除的CVE漏洞信息主键
     * @return 结果
     */
    @Override
    public int deleteCveInfoByCveIds(String[] cveIds)
    {
        return cveInfoMapper.deleteCveInfoByCveIds(cveIds);
    }

    /**
     * 删除CVE漏洞信息信息
     * 
     * @param cveId CVE漏洞信息主键
     * @return 结果
     */
    @Override
    public int deleteCveInfoByCveId(String cveId)
    {
        return cveInfoMapper.deleteCveInfoByCveId(cveId);
    }

    /**
     * 根据组件查找相关CVE
     * 
     * @param component 组件标识 (groupId:artifactId)
     * @return CVE列表
     */
    @Override
    public List<CveInfo> findCveByComponent(String component)
    {
        return cveInfoMapper.findCveByComponent(component);
    }

    /**
     * 批量插入CVE信息
     * 
     * @param cveInfoList CVE信息列表
     * @return 结果
     */
    @Override
    public int batchInsertCveInfo(List<CveInfo> cveInfoList)
    {
        if (cveInfoList == null || cveInfoList.isEmpty())
        {
            return 0;
        }
        
        return cveInfoMapper.batchInsertCveInfo(cveInfoList);
    }

    /**
     * 同步CVE数据
     * 
     * @return 同步结果
     */
    @Override
    public boolean syncCveData()
    {
        try
        {
            log.info("开始同步CVE数据...");
            
            // 从NVD API获取最新CVE数据
            List<CveInfo> cveList = fetchCveDataFromNvd();
            
            if (cveList != null && !cveList.isEmpty())
            {
                // 批量插入或更新CVE数据
                int insertCount = 0;
                int updateCount = 0;
                
                for (CveInfo cve : cveList)
                {
                    CveInfo existingCve = cveInfoMapper.selectCveInfoByCveId(cve.getCveId());
                    if (existingCve == null)
                    {
                        insertCveInfo(cve);
                        insertCount++;
                    }
                    else
                    {
                        // 更新现有CVE信息
                        cve.setCreateTime(existingCve.getCreateTime());
                        cve.setCreateBy(existingCve.getCreateBy());
                        updateCveInfo(cve);
                        updateCount++;
                    }
                }
                
                log.info("CVE数据同步完成，新增: {}, 更新: {}", insertCount, updateCount);
                return true;
            }
            else
            {
                log.warn("未获取到CVE数据");
                return false;
            }
        }
        catch (Exception e)
        {
            log.error("同步CVE数据失败", e);
            return false;
        }
    }

    /**
     * 从NVD API获取CVE数据
     * 
     * @return CVE列表
     */
    private List<CveInfo> fetchCveDataFromNvd()
    {
        List<CveInfo> cveList = new ArrayList<>();
        
        try
        {
            // 简化实现：获取最近的CVE数据
            // 实际应该支持增量同步和分页获取
            String url = "https://services.nvd.nist.gov/rest/json/cves/2.0?resultsPerPage=100";
            
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("User-Agent", "RuoYi-Security-Scanner/1.0");
            
            try (CloseableHttpResponse response = httpClient.execute(httpGet))
            {
                HttpEntity entity = response.getEntity();
                if (entity != null)
                {
                    String jsonResponse = EntityUtils.toString(entity);
                    cveList = parseCveResponse(jsonResponse);
                }
            }
        }
        catch (IOException e)
        {
            log.error("从NVD获取CVE数据失败", e);
        }
        
        return cveList;
    }

    /**
     * 解析NVD API响应
     * 
     * @param jsonResponse JSON响应
     * @return CVE列表
     */
    private List<CveInfo> parseCveResponse(String jsonResponse)
    {
        List<CveInfo> cveList = new ArrayList<>();
        
        try
        {
            JsonNode root = objectMapper.readTree(jsonResponse);
            JsonNode vulnerabilities = root.get("vulnerabilities");
            
            if (vulnerabilities != null && vulnerabilities.isArray())
            {
                for (JsonNode vulnNode : vulnerabilities)
                {
                    JsonNode cveNode = vulnNode.get("cve");
                    if (cveNode != null)
                    {
                        CveInfo cveInfo = parseSingleCve(cveNode);
                        if (cveInfo != null)
                        {
                            cveList.add(cveInfo);
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            log.error("解析CVE响应失败", e);
        }
        
        return cveList;
    }

    /**
     * 解析单个CVE信息
     * 
     * @param cveNode CVE节点
     * @return CVE信息
     */
    private CveInfo parseSingleCve(JsonNode cveNode)
    {
        try
        {
            CveInfo cveInfo = new CveInfo();
            
            // CVE ID
            cveInfo.setCveId(cveNode.get("id").asText());
            
            // 描述
            JsonNode descriptions = cveNode.get("descriptions");
            if (descriptions != null && descriptions.isArray() && descriptions.size() > 0)
            {
                cveInfo.setDescription(descriptions.get(0).get("value").asText());
            }
            
            // CVSS评分
            JsonNode metrics = cveNode.get("metrics");
            if (metrics != null)
            {
                JsonNode cvssV3 = metrics.get("cvssMetricV31");
                if (cvssV3 != null && cvssV3.isArray() && cvssV3.size() > 0)
                {
                    JsonNode cvssData = cvssV3.get(0).get("cvssData");
                    if (cvssData != null)
                    {
                        double baseScore = cvssData.get("baseScore").asDouble();
                        cveInfo.setCvssScore(BigDecimal.valueOf(baseScore));
                        
                        // 设置严重等级
                        if (baseScore >= 9.0) cveInfo.setSeverityLevel("4"); // Critical
                        else if (baseScore >= 7.0) cveInfo.setSeverityLevel("3"); // High
                        else if (baseScore >= 4.0) cveInfo.setSeverityLevel("2"); // Medium
                        else cveInfo.setSeverityLevel("1"); // Low
                    }
                }
            }
            
            // 发布时间
            String publishedDate = cveNode.get("published").asText();
            // 简化处理，实际应该解析ISO日期格式
            cveInfo.setPublishedDate(new Date());
            
            // 影响组件（简化处理）
            cveInfo.setAffectedComponents("{}");
            
            return cveInfo;
        }
        catch (Exception e)
        {
            log.debug("解析单个CVE失败", e);
            return null;
        }
    }
}
