package com.ruoyi.security.mapper;

import java.util.List;
import com.ruoyi.security.domain.ProjectDependency;

/**
 * 项目依赖Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface ProjectDependencyMapper 
{
    /**
     * 查询项目依赖
     * 
     * @param dependencyId 项目依赖主键
     * @return 项目依赖
     */
    public ProjectDependency selectProjectDependencyByDependencyId(Long dependencyId);

    /**
     * 查询项目依赖列表
     * 
     * @param projectDependency 项目依赖
     * @return 项目依赖集合
     */
    public List<ProjectDependency> selectProjectDependencyList(ProjectDependency projectDependency);

    /**
     * 新增项目依赖
     * 
     * @param projectDependency 项目依赖
     * @return 结果
     */
    public int insertProjectDependency(ProjectDependency projectDependency);

    /**
     * 修改项目依赖
     * 
     * @param projectDependency 项目依赖
     * @return 结果
     */
    public int updateProjectDependency(ProjectDependency projectDependency);

    /**
     * 删除项目依赖
     * 
     * @param dependencyId 项目依赖主键
     * @return 结果
     */
    public int deleteProjectDependencyByDependencyId(Long dependencyId);

    /**
     * 批量删除项目依赖
     * 
     * @param dependencyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProjectDependencyByDependencyIds(Long[] dependencyIds);

    /**
     * 批量插入项目依赖
     * 
     * @param dependencies 项目依赖列表
     * @return 结果
     */
    public int batchInsertProjectDependency(List<ProjectDependency> dependencies);

    /**
     * 根据任务ID查询依赖列表
     * 
     * @param taskId 任务ID
     * @return 依赖列表
     */
    public List<ProjectDependency> selectProjectDependencyByTaskId(Long taskId);
}
