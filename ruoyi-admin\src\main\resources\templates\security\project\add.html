<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增项目')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-project-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">项目名称：</label>
                <div class="col-sm-8">
                    <input name="projectName" class="form-control" type="text" placeholder="请输入项目名称" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">Git地址：</label>
                <div class="col-sm-8">
                    <input name="gitUrl" class="form-control" type="text" placeholder="请输入Git仓库地址" required>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 支持HTTP/HTTPS和SSH协议</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">分支：</label>
                <div class="col-sm-8">
                    <input name="branch" class="form-control" type="text" placeholder="请输入分支名称，默认为main" value="main">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">用户名：</label>
                <div class="col-sm-8">
                    <input name="username" class="form-control" type="text" placeholder="Git用户名（私有仓库需要）">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">密码/Token：</label>
                <div class="col-sm-8">
                    <input name="password" class="form-control" type="password" placeholder="Git密码或访问令牌（私有仓库需要）">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">描述：</label>
                <div class="col-sm-8">
                    <textarea name="description" class="form-control" rows="3" placeholder="请输入项目描述"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">状态：</label>
                <div class="col-sm-8">
                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_normal_disable')}">
                        <input type="radio" th:id="${dict.dictCode}" name="status" th:value="${dict.dictValue}" th:checked="${dict.default}">
                        <label th:for="${dict.dictCode}" th:text="${dict.dictLabel}"></label>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "security/project";
        $("#form-project-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix, $('#form-project-add').serialize());
            }
        }

        // 测试Git连接
        function testGitConnection() {
            var gitUrl = $("input[name='gitUrl']").val();
            var username = $("input[name='username']").val();
            var password = $("input[name='password']").val();
            var branch = $("input[name='branch']").val();
            
            if (!gitUrl) {
                $.modal.alertWarning("请先输入Git地址");
                return;
            }
            
            $.modal.loading("正在测试连接，请稍候...");
            $.ajax({
                url: prefix + "/testConnection",
                type: 'post',
                data: {
                    gitUrl: gitUrl,
                    username: username,
                    password: password,
                    branch: branch
                },
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess("连接测试成功");
                    } else {
                        $.modal.alertError("连接测试失败：" + result.msg);
                    }
                },
                error: function() {
                    $.modal.closeLoading();
                    $.modal.alertError("连接测试失败");
                }
            });
        }
        
        // 在Git地址输入框后添加测试按钮
        $(function() {
            var testBtn = '<div class="col-sm-1"><button type="button" class="btn btn-info btn-sm" onclick="testGitConnection()"><i class="fa fa-plug"></i> 测试</button></div>';
            $("input[name='gitUrl']").parent().removeClass('col-sm-8').addClass('col-sm-7').after(testBtn);
        });
    </script>
</body>
</html>
