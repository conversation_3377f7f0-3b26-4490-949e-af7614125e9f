package com.ruoyi.security.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.security.domain.ProjectInfo;
import com.ruoyi.security.service.IProjectInfoService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Date;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 项目信息控制器测试
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@WebMvcTest(ProjectInfoController.class)
public class ProjectInfoControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IProjectInfoService projectInfoService;

    @Autowired
    private ObjectMapper objectMapper;

    private ProjectInfo testProject;

    @BeforeEach
    void setUp() {
        testProject = new ProjectInfo();
        testProject.setProjectId(1L);
        testProject.setProjectName("测试项目");
        testProject.setProjectCode("TEST001");
        testProject.setGitUrl("https://gitlab.com/test/project.git");
        testProject.setGitBranch("master");
        testProject.setProjectDesc("测试项目描述");
        testProject.setCreateTime(new Date());
    }

    @Test
    @DisplayName("测试查询项目列表")
    @WithMockUser(authorities = {"security:project:list"})
    void testGetProjectList() throws Exception {
        // Mock服务返回
        when(projectInfoService.selectProjectInfoList(any(ProjectInfo.class)))
            .thenReturn(Arrays.asList(testProject));

        // 执行请求
        mockMvc.perform(get("/security/project/list")
                .param("projectName", "测试")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.rows").isArray())
                .andExpect(jsonPath("$.rows[0].projectName").value("测试项目"));

        // 验证服务调用
        verify(projectInfoService, times(1)).selectProjectInfoList(any(ProjectInfo.class));
    }

    @Test
    @DisplayName("测试获取项目详情")
    @WithMockUser(authorities = {"security:project:query"})
    void testGetProjectInfo() throws Exception {
        // Mock服务返回
        when(projectInfoService.selectProjectInfoByProjectId(1L))
            .thenReturn(testProject);

        // 执行请求
        mockMvc.perform(get("/security/project/1")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.projectName").value("测试项目"))
                .andExpect(jsonPath("$.data.projectCode").value("TEST001"));

        // 验证服务调用
        verify(projectInfoService, times(1)).selectProjectInfoByProjectId(1L);
    }

    @Test
    @DisplayName("测试新增项目")
    @WithMockUser(authorities = {"security:project:add"})
    void testAddProject() throws Exception {
        // Mock服务返回
        when(projectInfoService.insertProjectInfo(any(ProjectInfo.class)))
            .thenReturn(1);

        // 准备请求数据
        ProjectInfo newProject = new ProjectInfo();
        newProject.setProjectName("新项目");
        newProject.setProjectCode("NEW001");
        newProject.setGitUrl("https://gitlab.com/new/project.git");

        // 执行请求
        mockMvc.perform(post("/security/project")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(newProject)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));

        // 验证服务调用
        verify(projectInfoService, times(1)).insertProjectInfo(any(ProjectInfo.class));
    }

    @Test
    @DisplayName("测试修改项目")
    @WithMockUser(authorities = {"security:project:edit"})
    void testUpdateProject() throws Exception {
        // Mock服务返回
        when(projectInfoService.updateProjectInfo(any(ProjectInfo.class)))
            .thenReturn(1);

        // 准备请求数据
        testProject.setProjectName("修改后的项目名");

        // 执行请求
        mockMvc.perform(put("/security/project")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testProject)))
                .andExpected(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));

        // 验证服务调用
        verify(projectInfoService, times(1)).updateProjectInfo(any(ProjectInfo.class));
    }

    @Test
    @DisplayName("测试删除项目")
    @WithMockUser(authorities = {"security:project:remove"})
    void testDeleteProject() throws Exception {
        // Mock服务返回
        when(projectInfoService.deleteProjectInfoByProjectIds(any(Long[].class)))
            .thenReturn(1);

        // 执行请求
        mockMvc.perform(delete("/security/project/1,2")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("操作成功"));

        // 验证服务调用
        verify(projectInfoService, times(1)).deleteProjectInfoByProjectIds(any(Long[].class));
    }

    @Test
    @DisplayName("测试项目编码唯一性校验")
    @WithMockUser(authorities = {"security:project:query"})
    void testCheckProjectCodeUnique() throws Exception {
        // Mock服务返回
        when(projectInfoService.checkProjectCodeUnique(any(ProjectInfo.class)))
            .thenReturn("0");

        // 执行请求
        mockMvc.perform(post("/security/project/checkProjectCodeUnique")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testProject)))
                .andExpect(status().isOk())
                .andExpect(content().string("0"));

        // 验证服务调用
        verify(projectInfoService, times(1)).checkProjectCodeUnique(any(ProjectInfo.class));
    }

    @Test
    @DisplayName("测试Git连接测试")
    @WithMockUser(authorities = {"security:project:test"})
    void testGitConnectionTest() throws Exception {
        // Mock服务返回
        when(projectInfoService.testGitConnection(any(ProjectInfo.class)))
            .thenReturn(true);

        // 执行请求
        mockMvc.perform(post("/security/project/testConnection")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testProject)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.msg").value("Git连接测试成功"));

        // 验证服务调用
        verify(projectInfoService, times(1)).testGitConnection(any(ProjectInfo.class));
    }

    @Test
    @DisplayName("测试Git连接测试失败")
    @WithMockUser(authorities = {"security:project:test"})
    void testGitConnectionTestFailed() throws Exception {
        // Mock服务返回失败
        when(projectInfoService.testGitConnection(any(ProjectInfo.class)))
            .thenReturn(false);

        // 执行请求
        mockMvc.perform(post("/security/project/testConnection")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testProject)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("Git连接测试失败，请检查配置"));

        // 验证服务调用
        verify(projectInfoService, times(1)).testGitConnection(any(ProjectInfo.class));
    }

    @Test
    @DisplayName("测试无权限访问")
    @WithMockUser(authorities = {"other:permission"})
    void testUnauthorizedAccess() throws Exception {
        // 执行请求（无相应权限）
        mockMvc.perform(get("/security/project/list")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());

        // 验证服务未被调用
        verify(projectInfoService, never()).selectProjectInfoList(any(ProjectInfo.class));
    }

    @Test
    @DisplayName("测试参数验证失败")
    @WithMockUser(authorities = {"security:project:add"})
    void testParameterValidationFailed() throws Exception {
        // 准备无效数据（缺少必填字段）
        ProjectInfo invalidProject = new ProjectInfo();
        // 只设置项目名，缺少项目编码和Git地址

        // 执行请求
        mockMvc.perform(post("/security/project")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidProject)))
                .andExpect(status().isBadRequest());

        // 验证服务未被调用
        verify(projectInfoService, never()).insertProjectInfo(any(ProjectInfo.class));
    }

    @Test
    @DisplayName("测试服务异常处理")
    @WithMockUser(authorities = {"security:project:list"})
    void testServiceException() throws Exception {
        // Mock服务抛出异常
        when(projectInfoService.selectProjectInfoList(any(ProjectInfo.class)))
            .thenThrow(new RuntimeException("数据库连接失败"));

        // 执行请求
        mockMvc.perform(get("/security/project/list")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());

        // 验证服务被调用
        verify(projectInfoService, times(1)).selectProjectInfoList(any(ProjectInfo.class));
    }

    @Test
    @DisplayName("测试导出功能")
    @WithMockUser(authorities = {"security:project:export"})
    void testExportProject() throws Exception {
        // Mock服务返回
        when(projectInfoService.selectProjectInfoList(any(ProjectInfo.class)))
            .thenReturn(Arrays.asList(testProject));

        // 执行请求
        mockMvc.perform(post("/security/project/export")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(new ProjectInfo())))
                .andExpect(status().isOk());

        // 验证服务调用
        verify(projectInfoService, times(1)).selectProjectInfoList(any(ProjectInfo.class));
    }

    @Test
    @DisplayName("测试获取不存在的项目")
    @WithMockUser(authorities = {"security:project:query"})
    void testGetNonExistentProject() throws Exception {
        // Mock服务返回null
        when(projectInfoService.selectProjectInfoByProjectId(999L))
            .thenReturn(null);

        // 执行请求
        mockMvc.perform(get("/security/project/999")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.msg").value("项目不存在"));

        // 验证服务调用
        verify(projectInfoService, times(1)).selectProjectInfoByProjectId(999L);
    }
}
