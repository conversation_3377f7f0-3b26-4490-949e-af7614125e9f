-- 测试数据初始化脚本

-- 清理测试数据
DELETE FROM scan_report WHERE project_name LIKE '测试%';
DELETE FROM project_dependency WHERE task_id IN (SELECT task_id FROM scan_task WHERE task_name LIKE '测试%');
DELETE FROM scan_task WHERE task_name <PERSON>IKE '测试%';
DELETE FROM project_info WHERE project_name LIKE '测试%';
DELETE FROM cve_info WHERE cve_id LIKE 'CVE-TEST%';

-- 插入测试CVE数据
INSERT INTO cve_info (cve_id, description, cvss_score, severity_level, published_date, last_modified_date, create_by, create_time) VALUES
('CVE-TEST-2023-0001', 'Test CVE for Apache Commons Lang', 7.5, 'High', '2023-01-01', '2023-01-01', 'admin', NOW()),
('CVE-TEST-2023-0002', 'Test CVE for JUnit', 5.3, 'Medium', '2023-02-01', '2023-02-01', 'admin', NOW()),
('CVE-TEST-2023-0003', 'Test CVE for Spring Framework', 9.1, 'Critical', '2023-03-01', '2023-03-01', 'admin', NOW());

-- 插入测试项目数据
INSERT INTO project_info (project_name, project_code, git_url, git_branch, git_username, git_password, project_desc, project_owner, status, scan_status, create_by, create_time) VALUES
('测试项目1', 'TEST001', 'https://github.com/test/project1.git', 'master', 'testuser', 'testpass', '第一个测试项目', 'admin', '0', '0', 'admin', NOW()),
('测试项目2', 'TEST002', 'https://github.com/test/project2.git', 'develop', 'testuser', 'testpass', '第二个测试项目', 'admin', '0', '2', 'admin', NOW()),
('测试项目3', 'TEST003', 'https://gitlab.com/test/project3.git', 'main', 'testuser', 'testpass', '第三个测试项目', 'user', '0', '1', 'admin', NOW());

-- 获取项目ID（用于后续插入）
SET @project1_id = (SELECT project_id FROM project_info WHERE project_code = 'TEST001');
SET @project2_id = (SELECT project_id FROM project_info WHERE project_code = 'TEST002');
SET @project3_id = (SELECT project_id FROM project_info WHERE project_code = 'TEST003');

-- 插入测试扫描任务
INSERT INTO scan_task (project_id, task_name, task_status, start_time, end_time, error_message, create_by, create_time) VALUES
(@project1_id, '测试扫描任务1', '2', '2023-12-01 10:00:00', '2023-12-01 10:30:00', NULL, 'admin', NOW()),
(@project2_id, '测试扫描任务2', '2', '2023-12-02 14:00:00', '2023-12-02 14:45:00', NULL, 'admin', NOW()),
(@project3_id, '测试扫描任务3', '1', '2023-12-03 09:00:00', NULL, NULL, 'admin', NOW());

-- 获取任务ID
SET @task1_id = (SELECT task_id FROM scan_task WHERE task_name = '测试扫描任务1');
SET @task2_id = (SELECT task_id FROM scan_task WHERE task_name = '测试扫描任务2');
SET @task3_id = (SELECT task_id FROM scan_task WHERE task_name = '测试扫描任务3');

-- 插入测试项目依赖
INSERT INTO project_dependency (task_id, group_id, artifact_id, version, scope, has_cve, cve_count, max_cvss_score, license_name, license_risk_level, create_by, create_time) VALUES
(@task1_id, 'org.apache.commons', 'commons-lang3', '3.8.1', 'compile', '1', 1, 7.5, 'Apache-2.0', '1', 'admin', NOW()),
(@task1_id, 'junit', 'junit', '4.12', 'test', '1', 1, 5.3, 'EPL-1.0', '2', 'admin', NOW()),
(@task1_id, 'org.springframework', 'spring-core', '5.2.0', 'compile', '1', 1, 9.1, 'Apache-2.0', '1', 'admin', NOW()),
(@task2_id, 'com.fasterxml.jackson.core', 'jackson-core', '2.10.0', 'compile', '0', 0, 0.0, 'Apache-2.0', '1', 'admin', NOW()),
(@task2_id, 'org.slf4j', 'slf4j-api', '1.7.30', 'compile', '0', 0, 0.0, 'MIT', '1', 'admin', NOW()),
(@task2_id, 'mysql', 'mysql-connector-java', '8.0.20', 'runtime', '0', 0, 0.0, 'GPL-2.0', '3', 'admin', NOW());

-- 插入测试扫描报告
INSERT INTO scan_report (task_id, project_id, project_name, project_code, scan_time, total_dependencies, cve_dependencies, high_risk_dependencies, medium_risk_dependencies, low_risk_dependencies, license_risk_dependencies, report_content, create_by, create_time) VALUES
(@task1_id, @project1_id, '测试项目1', 'TEST001', '2023-12-01 10:30:00', 3, 3, 1, 1, 1, 1, '{"summary":{"totalDeps":3,"cveDeps":3,"highRisk":1},"details":[]}', 'admin', NOW()),
(@task2_id, @project2_id, '测试项目2', 'TEST002', '2023-12-02 14:45:00', 3, 0, 0, 0, 3, 1, '{"summary":{"totalDeps":3,"cveDeps":0,"highRisk":0},"details":[]}', 'admin', NOW());

-- 插入测试用户权限数据（如果需要）
-- 这里可以根据实际需要添加测试用户和权限数据

-- 提交事务
COMMIT;
