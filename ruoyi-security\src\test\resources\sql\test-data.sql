-- 测试数据初始化脚本

-- 清理测试数据
DELETE FROM scan_report WHERE project_name LIKE '测试%';
DELETE FROM project_dependency WHERE task_id IN (SELECT task_id FROM scan_task WHERE task_name LIKE '测试%');
DELETE FROM scan_task WHERE task_name LIKE '测试%';
DELETE FROM project_info WHERE project_name LIKE '测试%';
DELETE FROM cve_info WHERE cve_id LIKE 'CVE-TEST%';

-- 插入测试字典数据
INSERT INTO sys_dict_type (dict_id, dict_name, dict_type, status, create_by, create_time, remark) VALUES 
(100, '许可证风险等级', 'license_risk_level', '0', 'admin', sysdate(), '许可证风险等级字典'),
(101, 'CVE严重等级', 'cve_severity_level', '0', 'admin', sysdate(), 'CVE严重等级字典'),
(102, '扫描状态', 'scan_status', '0', 'admin', sysdate(), '扫描状态字典');

INSERT INTO sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(100, 1, '低风险', '1', 'license_risk_level', '', 'success', 'Y', '0', 'admin', sysdate(), '低风险许可证'),
(101, 2, '中风险', '2', 'license_risk_level', '', 'warning', 'N', '0', 'admin', sysdate(), '中风险许可证'),
(102, 3, '高风险', '3', 'license_risk_level', '', 'danger', 'N', '0', 'admin', sysdate(), '高风险许可证'),
(103, 1, '无', 'None', 'cve_severity_level', '', 'info', 'Y', '0', 'admin', sysdate(), '无CVE'),
(104, 2, '低', 'Low', 'cve_severity_level', '', 'success', 'N', '0', 'admin', sysdate(), '低危CVE'),
(105, 3, '中', 'Medium', 'cve_severity_level', '', 'warning', 'N', '0', 'admin', sysdate(), '中危CVE'),
(106, 4, '高', 'High', 'cve_severity_level', '', 'danger', 'N', '0', 'admin', sysdate(), '高危CVE'),
(107, 5, '严重', 'Critical', 'cve_severity_level', '', 'danger', 'N', '0', 'admin', sysdate(), '严重CVE'),
(108, 1, '待扫描', '0', 'scan_status', '', 'info', 'Y', '0', 'admin', sysdate(), '待扫描状态'),
(109, 2, '扫描中', '1', 'scan_status', '', 'warning', 'N', '0', 'admin', sysdate(), '扫描中状态'),
(110, 3, '已完成', '2', 'scan_status', '', 'success', 'N', '0', 'admin', sysdate(), '扫描完成状态'),
(111, 4, '扫描失败', '3', 'scan_status', '', 'danger', 'N', '0', 'admin', sysdate(), '扫描失败状态');

-- 插入测试CVE数据
INSERT INTO cve_info (cve_id, description, cvss_score, severity_level, published_date, last_modified_date, create_by, create_time) VALUES 
('CVE-TEST-2023-0001', 'Test CVE for Apache Commons Lang', 7.5, 'High', '2023-01-01', '2023-01-01', 'admin', sysdate()),
('CVE-TEST-2023-0002', 'Test CVE for JUnit', 5.3, 'Medium', '2023-02-01', '2023-02-01', 'admin', sysdate()),
('CVE-TEST-2023-0003', 'Test CVE for Spring Framework', 9.1, 'Critical', '2023-03-01', '2023-03-01', 'admin', sysdate());

-- 插入测试项目数据
INSERT INTO project_info (project_name, project_code, git_url, git_branch, git_username, git_password, project_desc, project_owner, status, scan_status, create_by, create_time) VALUES 
('测试项目1', 'TEST001', 'https://github.com/test/project1.git', 'master', 'testuser', 'testpass', '第一个测试项目', 'admin', '0', '0', 'admin', sysdate()),
('测试项目2', 'TEST002', 'https://github.com/test/project2.git', 'develop', 'testuser', 'testpass', '第二个测试项目', 'admin', '0', '2', 'admin', sysdate()),
('测试项目3', 'TEST003', 'https://gitlab.com/test/project3.git', 'main', 'testuser', 'testpass', '第三个测试项目', 'user', '0', '1', 'admin', sysdate());

-- 获取项目ID（用于后续插入）
SET @project1_id = (SELECT project_id FROM project_info WHERE project_code = 'TEST001');
SET @project2_id = (SELECT project_id FROM project_info WHERE project_code = 'TEST002');
SET @project3_id = (SELECT project_id FROM project_info WHERE project_code = 'TEST003');

-- 插入测试扫描任务
INSERT INTO scan_task (project_id, task_name, task_status, start_time, end_time, error_message, create_by, create_time) VALUES 
(@project1_id, '测试扫描任务1', '2', '2023-12-01 10:00:00', '2023-12-01 10:30:00', NULL, 'admin', sysdate()),
(@project2_id, '测试扫描任务2', '2', '2023-12-02 14:00:00', '2023-12-02 14:45:00', NULL, 'admin', sysdate()),
(@project3_id, '测试扫描任务3', '1', '2023-12-03 09:00:00', NULL, NULL, 'admin', sysdate());

-- 获取任务ID
SET @task1_id = (SELECT task_id FROM scan_task WHERE task_name = '测试扫描任务1');
SET @task2_id = (SELECT task_id FROM scan_task WHERE task_name = '测试扫描任务2');
SET @task3_id = (SELECT task_id FROM scan_task WHERE task_name = '测试扫描任务3');

-- 插入测试项目依赖
INSERT INTO project_dependency (task_id, group_id, artifact_id, version, scope, has_cve, cve_count, max_cvss_score, license_name, license_risk_level, create_by, create_time) VALUES 
(@task1_id, 'org.apache.commons', 'commons-lang3', '3.8.1', 'compile', '1', 1, 7.5, 'Apache-2.0', '1', 'admin', sysdate()),
(@task1_id, 'junit', 'junit', '4.12', 'test', '1', 1, 5.3, 'EPL-1.0', '2', 'admin', sysdate()),
(@task1_id, 'org.springframework', 'spring-core', '5.2.0', 'compile', '1', 1, 9.1, 'Apache-2.0', '1', 'admin', sysdate()),
(@task2_id, 'com.fasterxml.jackson.core', 'jackson-core', '2.10.0', 'compile', '0', 0, 0.0, 'Apache-2.0', '1', 'admin', sysdate()),
(@task2_id, 'org.slf4j', 'slf4j-api', '1.7.30', 'compile', '0', 0, 0.0, 'MIT', '1', 'admin', sysdate()),
(@task2_id, 'mysql', 'mysql-connector-java', '8.0.20', 'runtime', '0', 0, 0.0, 'GPL-2.0', '3', 'admin', sysdate());

-- 插入测试扫描报告
INSERT INTO scan_report (task_id, project_id, project_name, project_code, scan_time, total_dependencies, cve_dependencies, high_risk_dependencies, medium_risk_dependencies, low_risk_dependencies, license_risk_dependencies, report_content, create_by, create_time) VALUES 
(@task1_id, @project1_id, '测试项目1', 'TEST001', '2023-12-01 10:30:00', 3, 3, 1, 1, 1, 1, '{"summary":{"totalDeps":3,"cveDeps":3,"highRisk":1},"details":[]}', 'admin', sysdate()),
(@task2_id, @project2_id, '测试项目2', 'TEST002', '2023-12-02 14:45:00', 3, 0, 0, 0, 3, 1, '{"summary":{"totalDeps":3,"cveDeps":0,"highRisk":0},"details":[]}', 'admin', sysdate());

-- 插入测试用户权限数据（如果需要）
-- 这里可以根据实际需要添加测试用户和权限数据

-- 提交事务
COMMIT;
