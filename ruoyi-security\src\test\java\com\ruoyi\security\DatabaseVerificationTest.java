package com.ruoyi.security;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 数据库功能验证测试
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class DatabaseVerificationTest {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        cleanupTestData();
    }

    /**
     * 验证项目信息表结构
     */
    @Test
    void testProjectInfoTableStructure() {
        // 验证表存在
        String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'project_info' AND table_schema = DATABASE()";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "项目信息表应该存在");

        // 验证主键约束
        sql = "SELECT COUNT(*) FROM information_schema.key_column_usage " +
              "WHERE table_name = 'project_info' AND constraint_name = 'PRIMARY' AND table_schema = DATABASE()";
        count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "项目信息表应该有主键");

        // 验证唯一约束
        sql = "SELECT COUNT(*) FROM information_schema.key_column_usage " +
              "WHERE table_name = 'project_info' AND constraint_name = 'uk_project_code' AND table_schema = DATABASE()";
        count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "项目编码应该有唯一约束");
    }

    /**
     * 验证扫描任务表结构
     */
    @Test
    void testScanTaskTableStructure() {
        // 验证表存在
        String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'scan_task' AND table_schema = DATABASE()";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "扫描任务表应该存在");

        // 验证索引存在
        sql = "SELECT COUNT(*) FROM information_schema.statistics " +
              "WHERE table_name = 'scan_task' AND index_name = 'idx_project_id' AND table_schema = DATABASE()";
        count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "项目ID应该有索引");
    }

    /**
     * 验证CVE信息表结构
     */
    @Test
    void testCveInfoTableStructure() {
        // 验证表存在
        String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'cve_info' AND table_schema = DATABASE()";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "CVE信息表应该存在");

        // 验证唯一约束
        sql = "SELECT COUNT(*) FROM information_schema.key_column_usage " +
              "WHERE table_name = 'cve_info' AND constraint_name = 'uk_cve_id' AND table_schema = DATABASE()";
        count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "CVE ID应该有唯一约束");
    }

    /**
     * 验证项目依赖表结构
     */
    @Test
    void testProjectDependencyTableStructure() {
        // 验证表存在
        String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'project_dependency' AND table_schema = DATABASE()";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "项目依赖表应该存在");

        // 验证索引存在
        sql = "SELECT COUNT(*) FROM information_schema.statistics " +
              "WHERE table_name = 'project_dependency' AND index_name = 'idx_task_id' AND table_schema = DATABASE()";
        count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "任务ID应该有索引");
    }

    /**
     * 验证扫描报告表结构
     */
    @Test
    void testScanReportTableStructure() {
        // 验证表存在
        String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'scan_report' AND table_schema = DATABASE()";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "扫描报告表应该存在");

        // 验证索引存在
        sql = "SELECT COUNT(*) FROM information_schema.statistics " +
              "WHERE table_name = 'scan_report' AND index_name = 'idx_project_name' AND table_schema = DATABASE()";
        count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "项目名称应该有索引");
    }

    /**
     * 验证字典数据
     */
    @Test
    void testDictionaryData() {
        // 验证许可证风险等级字典
        String sql = "SELECT COUNT(*) FROM sys_dict_type WHERE dict_type = 'license_risk_level'";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "许可证风险等级字典类型应该存在");

        sql = "SELECT COUNT(*) FROM sys_dict_data WHERE dict_type = 'license_risk_level'";
        count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertTrue(count >= 3, "许可证风险等级字典数据应该至少有3条");

        // 验证CVE严重等级字典
        sql = "SELECT COUNT(*) FROM sys_dict_type WHERE dict_type = 'cve_severity_level'";
        count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "CVE严重等级字典类型应该存在");

        // 验证扫描状态字典
        sql = "SELECT COUNT(*) FROM sys_dict_type WHERE dict_type = 'scan_status'";
        count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "扫描状态字典类型应该存在");
    }

    /**
     * 验证数据插入操作
     */
    @Test
    void testDataInsertOperations() {
        // 插入测试项目
        String sql = "INSERT INTO project_info (project_name, project_code, git_url, create_by) " +
                    "VALUES ('测试项目', 'TEST001', 'https://gitlab.com/test/project.git', 'admin')";
        int result = jdbcTemplate.update(sql);
        assertEquals(1, result, "应该成功插入一条项目记录");

        // 验证插入的数据
        sql = "SELECT COUNT(*) FROM project_info WHERE project_code = 'TEST001'";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "应该能查询到插入的项目");

        // 测试唯一约束
        assertThrows(Exception.class, () -> {
            String duplicateSql = "INSERT INTO project_info (project_name, project_code, git_url, create_by) " +
                                 "VALUES ('重复项目', 'TEST001', 'https://gitlab.com/test/duplicate.git', 'admin')";
            jdbcTemplate.update(duplicateSql);
        }, "重复的项目编码应该抛出异常");
    }

    /**
     * 验证数据查询操作
     */
    @Test
    void testDataQueryOperations() {
        // 先插入测试数据
        insertTestData();

        // 测试基本查询
        String sql = "SELECT COUNT(*) FROM project_info WHERE del_flag = '0'";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertTrue(count > 0, "应该能查询到未删除的项目");

        // 测试连接查询
        sql = "SELECT COUNT(*) FROM scan_task st " +
              "INNER JOIN project_info pi ON st.project_id = pi.project_id " +
              "WHERE pi.del_flag = '0'";
        count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertTrue(count >= 0, "连接查询应该正常执行");
    }

    /**
     * 验证数据更新操作
     */
    @Test
    void testDataUpdateOperations() {
        // 先插入测试数据
        insertTestData();

        // 更新项目状态
        String sql = "UPDATE project_info SET scan_status = '1', update_by = 'admin' " +
                    "WHERE project_code = 'TEST001'";
        int result = jdbcTemplate.update(sql);
        assertEquals(1, result, "应该成功更新一条记录");

        // 验证更新结果
        sql = "SELECT scan_status FROM project_info WHERE project_code = 'TEST001'";
        String status = jdbcTemplate.queryForObject(sql, String.class);
        assertEquals("1", status, "扫描状态应该已更新");
    }

    /**
     * 验证软删除操作
     */
    @Test
    void testSoftDeleteOperations() {
        // 先插入测试数据
        insertTestData();

        // 执行软删除
        String sql = "UPDATE project_info SET del_flag = '2', update_by = 'admin' " +
                    "WHERE project_code = 'TEST001'";
        int result = jdbcTemplate.update(sql);
        assertEquals(1, result, "应该成功软删除一条记录");

        // 验证软删除结果
        sql = "SELECT COUNT(*) FROM project_info WHERE project_code = 'TEST001' AND del_flag = '0'";
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(0, count, "软删除后查询正常数据应该为0");

        sql = "SELECT COUNT(*) FROM project_info WHERE project_code = 'TEST001' AND del_flag = '2'";
        count = jdbcTemplate.queryForObject(sql, Integer.class);
        assertEquals(1, count, "软删除后查询删除数据应该为1");
    }

    /**
     * 插入测试数据
     */
    private void insertTestData() {
        // 插入测试项目
        String sql = "INSERT INTO project_info (project_name, project_code, git_url, create_by) " +
                    "VALUES ('测试项目', 'TEST001', 'https://gitlab.com/test/project.git', 'admin')";
        jdbcTemplate.update(sql);

        // 获取项目ID
        sql = "SELECT project_id FROM project_info WHERE project_code = 'TEST001'";
        Long projectId = jdbcTemplate.queryForObject(sql, Long.class);

        // 插入测试任务
        sql = "INSERT INTO scan_task (project_id, task_name, create_by) " +
              "VALUES (?, '测试扫描任务', 'admin')";
        jdbcTemplate.update(sql, projectId);
    }

    /**
     * 清理测试数据
     */
    private void cleanupTestData() {
        jdbcTemplate.update("DELETE FROM scan_report WHERE project_name LIKE '测试%'");
        jdbcTemplate.update("DELETE FROM project_dependency WHERE task_id IN " +
                           "(SELECT task_id FROM scan_task WHERE task_name LIKE '测试%')");
        jdbcTemplate.update("DELETE FROM scan_task WHERE task_name LIKE '测试%'");
        jdbcTemplate.update("DELETE FROM project_info WHERE project_name LIKE '测试%'");
    }
}
