-- 安全扫描系统数据库脚本
-- 基于RuoYi框架的安全扫描管理系统

-- 项目信息表
DROP TABLE IF EXISTS `project_info`;
CREATE TABLE `project_info` (
  `project_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `project_code` varchar(50) NOT NULL COMMENT '项目编码',
  `git_url` varchar(500) NOT NULL COMMENT 'GitLab地址',
  `git_branch` varchar(100) DEFAULT 'master' COMMENT 'Git分支',
  `git_username` varchar(100) COMMENT 'GitLab用户名',
  `git_password` varchar(200) COMMENT 'GitLab密码或Token(加密)',
  `project_desc` text COMMENT '项目描述',
  `project_owner` varchar(50) COMMENT '项目负责人',
  `scan_status` char(1) DEFAULT '0' COMMENT '扫描状态(0未扫描 1扫描中 2已完成 3扫描失败)',
  `last_scan_time` datetime COMMENT '最后扫描时间',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志(0代表存在 2代表删除)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`project_id`),
  UNIQUE KEY `uk_project_code` (`project_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='项目信息表';

-- 扫描任务表
DROP TABLE IF EXISTS `scan_task`;
CREATE TABLE `scan_task` (
  `task_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_status` char(1) DEFAULT '0' COMMENT '任务状态(0待执行 1执行中 2已完成 3执行失败)',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `total_dependencies` int(11) DEFAULT 0 COMMENT '总依赖数',
  `cve_high_count` int(11) DEFAULT 0 COMMENT '高危CVE数量',
  `cve_medium_count` int(11) DEFAULT 0 COMMENT '中危CVE数量',
  `cve_low_count` int(11) DEFAULT 0 COMMENT '低危CVE数量',
  `license_high_risk_count` int(11) DEFAULT 0 COMMENT '高风险许可证数量',
  `license_medium_risk_count` int(11) DEFAULT 0 COMMENT '中风险许可证数量',
  `error_msg` text COMMENT '错误信息',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`task_id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='扫描任务表';

-- CVE漏洞信息表
DROP TABLE IF EXISTS `cve_info`;
CREATE TABLE `cve_info` (
  `cve_id` varchar(20) NOT NULL COMMENT 'CVE编号',
  `cve_desc` text COMMENT 'CVE描述',
  `cvss_score` decimal(3,1) COMMENT 'CVSS评分',
  `severity_level` char(1) COMMENT '严重等级(1低危 2中危 3高危 4严重)',
  `publish_date` date COMMENT '发布日期',
  `update_date` date COMMENT '更新日期',
  `affected_components` text COMMENT '影响组件(JSON格式)',
  `references` text COMMENT '参考链接(JSON格式)',
  `status` char(1) DEFAULT '0' COMMENT '状态(0正常 1停用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`cve_id`)
) ENGINE=InnoDB COMMENT='CVE漏洞信息表';

-- 项目依赖表
DROP TABLE IF EXISTS `project_dependency`;
CREATE TABLE `project_dependency` (
  `dependency_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '依赖ID',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `task_id` bigint(20) NOT NULL COMMENT '扫描任务ID',
  `group_id` varchar(200) NOT NULL COMMENT 'Maven GroupId',
  `artifact_id` varchar(200) NOT NULL COMMENT 'Maven ArtifactId',
  `version` varchar(50) NOT NULL COMMENT '版本号',
  `license_type` varchar(100) COMMENT '许可证类型',
  `license_risk_level` char(1) COMMENT '许可证风险等级(1低风险 2中风险 3高风险)',
  `has_cve` char(1) DEFAULT '0' COMMENT '是否存在CVE(0否 1是)',
  `cve_count` int(11) DEFAULT 0 COMMENT 'CVE数量',
  `max_cvss_score` decimal(3,1) COMMENT '最高CVSS评分',
  `cve_details` text COMMENT 'CVE详情(JSON格式)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`dependency_id`),
  KEY `idx_project_task` (`project_id`, `task_id`),
  KEY `idx_group_artifact` (`group_id`, `artifact_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='项目依赖表';

-- 扫描报告表
DROP TABLE IF EXISTS `scan_report`;
CREATE TABLE `scan_report` (
  `report_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '报告ID',
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `report_content` longtext COMMENT '报告内容(JSON格式)',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`report_id`),
  KEY `idx_project_name` (`project_name`),
  KEY `idx_project_task` (`project_id`, `task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='扫描报告表';

-- 字典数据配置
-- 许可证风险等级字典
INSERT INTO sys_dict_type VALUES (100, '许可证风险等级', 'license_risk_level', '0', 'admin', sysdate(), '', null, '许可证风险等级分类');
INSERT INTO sys_dict_data VALUES (100, 1, '低风险', '1', 'license_risk_level', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '低风险许可证');
INSERT INTO sys_dict_data VALUES (101, 2, '中风险', '2', 'license_risk_level', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '中风险许可证');
INSERT INTO sys_dict_data VALUES (102, 3, '高风险', '3', 'license_risk_level', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '高风险许可证(GPL/AGPL等)');

-- CVE严重等级字典
INSERT INTO sys_dict_type VALUES (101, 'CVE严重等级', 'cve_severity_level', '0', 'admin', sysdate(), '', null, 'CVE漏洞严重等级');
INSERT INTO sys_dict_data VALUES (103, 1, '低危', '1', 'cve_severity_level', '', 'info', 'N', '0', 'admin', sysdate(), '', null, 'CVSS评分0.1-3.9');
INSERT INTO sys_dict_data VALUES (104, 2, '中危', '2', 'cve_severity_level', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, 'CVSS评分4.0-6.9');
INSERT INTO sys_dict_data VALUES (105, 3, '高危', '3', 'cve_severity_level', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, 'CVSS评分7.0-8.9');
INSERT INTO sys_dict_data VALUES (106, 4, '严重', '4', 'cve_severity_level', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, 'CVSS评分9.0-10.0');

-- 扫描状态字典
INSERT INTO sys_dict_type VALUES (102, '扫描状态', 'scan_status', '0', 'admin', sysdate(), '', null, '项目扫描状态');
INSERT INTO sys_dict_data VALUES (107, 1, '未扫描', '0', 'scan_status', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '项目未进行扫描');
INSERT INTO sys_dict_data VALUES (108, 2, '扫描中', '1', 'scan_status', '', 'primary', 'N', '0', 'admin', sysdate(), '', null, '项目正在扫描');
INSERT INTO sys_dict_data VALUES (109, 3, '已完成', '2', 'scan_status', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '扫描已完成');
INSERT INTO sys_dict_data VALUES (110, 4, '扫描失败', '3', 'scan_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '扫描执行失败');

-- 任务状态字典
INSERT INTO sys_dict_type VALUES (103, '任务状态', 'task_status', '0', 'admin', sysdate(), '', null, '扫描任务状态');
INSERT INTO sys_dict_data VALUES (111, 1, '待执行', '0', 'task_status', '', 'info', 'N', '0', 'admin', sysdate(), '', null, '任务待执行');
INSERT INTO sys_dict_data VALUES (112, 2, '执行中', '1', 'task_status', '', 'primary', 'N', '0', 'admin', sysdate(), '', null, '任务执行中');
INSERT INTO sys_dict_data VALUES (113, 3, '已完成', '2', 'task_status', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '任务已完成');
INSERT INTO sys_dict_data VALUES (114, 4, '执行失败', '3', 'task_status', '', 'danger', 'N', '0', 'admin', sysdate(), '', null, '任务执行失败');

-- 许可证类型基础数据(重点关注GPL/AGPL)
INSERT INTO sys_dict_type VALUES (104, '许可证类型', 'license_type', '0', 'admin', sysdate(), '', null, '开源许可证类型');
INSERT INTO sys_dict_data VALUES (115, 1, 'Apache-2.0', 'Apache-2.0', 'license_type', '1', '', 'N', '0', 'admin', sysdate(), '', null, 'Apache许可证2.0版本');
INSERT INTO sys_dict_data VALUES (116, 2, 'MIT', 'MIT', 'license_type', '1', '', 'N', '0', 'admin', sysdate(), '', null, 'MIT许可证');
INSERT INTO sys_dict_data VALUES (117, 3, 'BSD-3-Clause', 'BSD-3-Clause', 'license_type', '1', '', 'N', '0', 'admin', sysdate(), '', null, 'BSD 3条款许可证');
INSERT INTO sys_dict_data VALUES (118, 4, 'GPL-2.0', 'GPL-2.0', 'license_type', '3', '', 'N', '0', 'admin', sysdate(), '', null, 'GNU通用公共许可证v2');
INSERT INTO sys_dict_data VALUES (119, 5, 'GPL-3.0', 'GPL-3.0', 'license_type', '3', '', 'N', '0', 'admin', sysdate(), '', null, 'GNU通用公共许可证v3');
INSERT INTO sys_dict_data VALUES (120, 6, 'AGPL-3.0', 'AGPL-3.0', 'license_type', '3', '', 'N', '0', 'admin', sysdate(), '', null, 'GNU Affero通用公共许可证v3');
INSERT INTO sys_dict_data VALUES (121, 7, 'LGPL-2.1', 'LGPL-2.1', 'license_type', '2', '', 'N', '0', 'admin', sysdate(), '', null, 'GNU宽通用公共许可证v2.1');
INSERT INTO sys_dict_data VALUES (122, 8, 'LGPL-3.0', 'LGPL-3.0', 'license_type', '2', '', 'N', '0', 'admin', sysdate(), '', null, 'GNU宽通用公共许可证v3');
