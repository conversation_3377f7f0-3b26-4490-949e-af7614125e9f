<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('安全仪表板')" />
    <th:block th:include="include :: layout-latest-css" />
    <style>
        .dashboard-card {
            background: #fff;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 5px;
            color: white;
            margin-bottom: 20px;
        }
        .stat-card h3 {
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }
        .stat-card p {
            margin: 5px 0 0 0;
            font-size: 1.1em;
        }
        .stat-projects { background: linear-gradient(45deg, #3498db, #2980b9); }
        .stat-scans { background: linear-gradient(45deg, #2ecc71, #27ae60); }
        .stat-vulnerabilities { background: linear-gradient(45deg, #e74c3c, #c0392b); }
        .stat-licenses { background: linear-gradient(45deg, #f39c12, #e67e22); }
        
        .chart-container {
            height: 300px;
            margin: 20px 0;
        }
        .recent-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .recent-item:last-child {
            border-bottom: none;
        }
        .risk-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .risk-high { background-color: #e74c3c; }
        .risk-medium { background-color: #f39c12; }
        .risk-low { background-color: #2ecc71; }
        .risk-safe { background-color: #95a5a6; }
    </style>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <!-- 统计卡片 -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card stat-projects">
                    <h3 id="totalProjects">0</h3>
                    <p>项目总数</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card stat-scans">
                    <h3 id="totalScans">0</h3>
                    <p>扫描总数</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card stat-vulnerabilities">
                    <h3 id="totalVulnerabilities">0</h3>
                    <p>发现漏洞</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card stat-licenses">
                    <h3 id="riskLicenses">0</h3>
                    <p>风险许可证</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 风险分布图表 -->
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <h4>风险分布统计</h4>
                    <div class="chart-container">
                        <canvas id="riskChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 扫描状态 -->
            <div class="col-lg-4">
                <div class="dashboard-card">
                    <h4>扫描状态</h4>
                    <div class="chart-container">
                        <canvas id="scanStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 最近扫描 -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <h4>最近扫描</h4>
                    <div id="recentScans">
                        <div class="text-center">
                            <i class="fa fa-spinner fa-spin"></i> 加载中...
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 高风险项目 -->
            <div class="col-lg-6">
                <div class="dashboard-card">
                    <h4>高风险项目</h4>
                    <div id="riskProjects">
                        <div class="text-center">
                            <i class="fa fa-spinner fa-spin"></i> 加载中...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CVE趋势图表 -->
        <div class="row">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <h4>CVE发现趋势（最近30天）</h4>
                    <div class="chart-container">
                        <canvas id="cveTrendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <th:block th:include="include :: footer" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script th:inline="javascript">
        var prefix = ctx + "security/dashboard";
        
        $(function() {
            loadDashboardData();
            // 每30秒刷新一次数据
            setInterval(loadDashboardData, 30000);
        });

        function loadDashboardData() {
            $.ajax({
                url: prefix + "/data",
                type: 'get',
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        updateStatistics(result.data);
                        updateCharts(result.data);
                        updateRecentScans(result.data.recentScans);
                        updateRiskProjects(result.data.riskProjects);
                    }
                }
            });
        }

        function updateStatistics(data) {
            $('#totalProjects').text(data.totalProjects || 0);
            $('#totalScans').text(data.totalScans || 0);
            $('#totalVulnerabilities').text(data.totalVulnerabilities || 0);
            $('#riskLicenses').text(data.riskLicenses || 0);
        }

        function updateCharts(data) {
            // 风险分布图表
            var riskCtx = document.getElementById('riskChart').getContext('2d');
            new Chart(riskCtx, {
                type: 'doughnut',
                data: {
                    labels: ['高风险', '中风险', '低风险', '安全'],
                    datasets: [{
                        data: [
                            data.riskDistribution?.high || 0,
                            data.riskDistribution?.medium || 0,
                            data.riskDistribution?.low || 0,
                            data.riskDistribution?.safe || 0
                        ],
                        backgroundColor: ['#e74c3c', '#f39c12', '#3498db', '#2ecc71']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 扫描状态图表
            var statusCtx = document.getElementById('scanStatusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'pie',
                data: {
                    labels: ['已完成', '进行中', '待扫描', '失败'],
                    datasets: [{
                        data: [
                            data.scanStatus?.completed || 0,
                            data.scanStatus?.running || 0,
                            data.scanStatus?.pending || 0,
                            data.scanStatus?.failed || 0
                        ],
                        backgroundColor: ['#2ecc71', '#3498db', '#f39c12', '#e74c3c']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // CVE趋势图表
            var trendCtx = document.getElementById('cveTrendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: data.cveTrend?.dates || [],
                    datasets: [{
                        label: '新发现CVE',
                        data: data.cveTrend?.counts || [],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateRecentScans(scans) {
            var html = '';
            if (scans && scans.length > 0) {
                scans.forEach(function(scan) {
                    var statusClass = scan.scanStatus === 'COMPLETED' ? 'success' : 
                                    scan.scanStatus === 'RUNNING' ? 'info' : 
                                    scan.scanStatus === 'FAILED' ? 'danger' : 'warning';
                    html += '<div class="recent-item">';
                    html += '<div class="row">';
                    html += '<div class="col-md-8">';
                    html += '<strong>' + scan.projectName + '</strong><br>';
                    html += '<small class="text-muted">' + scan.scanTime + '</small>';
                    html += '</div>';
                    html += '<div class="col-md-4 text-right">';
                    html += '<span class="label label-' + statusClass + '">' + scan.scanStatusText + '</span>';
                    html += '</div>';
                    html += '</div>';
                    html += '</div>';
                });
            } else {
                html = '<div class="text-center text-muted">暂无扫描记录</div>';
            }
            $('#recentScans').html(html);
        }

        function updateRiskProjects(projects) {
            var html = '';
            if (projects && projects.length > 0) {
                projects.forEach(function(project) {
                    var riskClass = project.riskLevel === 'HIGH' ? 'risk-high' : 
                                  project.riskLevel === 'MEDIUM' ? 'risk-medium' : 'risk-low';
                    html += '<div class="recent-item">';
                    html += '<span class="risk-indicator ' + riskClass + '"></span>';
                    html += '<strong>' + project.projectName + '</strong><br>';
                    html += '<small class="text-muted">CVE: ' + project.cveCount + ' | 许可证风险: ' + project.licenseRiskCount + '</small>';
                    html += '</div>';
                });
            } else {
                html = '<div class="text-center text-muted">暂无高风险项目</div>';
            }
            $('#riskProjects').html(html);
        }
    </script>
</body>
</html>
