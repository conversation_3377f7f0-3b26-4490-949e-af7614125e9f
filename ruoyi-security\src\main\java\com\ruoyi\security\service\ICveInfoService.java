package com.ruoyi.security.service;

import java.util.List;
import com.ruoyi.security.domain.CveInfo;

/**
 * CVE漏洞信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface ICveInfoService 
{
    /**
     * 查询CVE漏洞信息
     * 
     * @param cveId CVE漏洞信息主键
     * @return CVE漏洞信息
     */
    public CveInfo selectCveInfoByCveId(String cveId);

    /**
     * 查询CVE漏洞信息列表
     * 
     * @param cveInfo CVE漏洞信息
     * @return CVE漏洞信息集合
     */
    public List<CveInfo> selectCveInfoList(CveInfo cveInfo);

    /**
     * 新增CVE漏洞信息
     * 
     * @param cveInfo CVE漏洞信息
     * @return 结果
     */
    public int insertCveInfo(CveInfo cveInfo);

    /**
     * 修改CVE漏洞信息
     * 
     * @param cveInfo CVE漏洞信息
     * @return 结果
     */
    public int updateCveInfo(CveInfo cveInfo);

    /**
     * 批量删除CVE漏洞信息
     * 
     * @param cveIds 需要删除的CVE漏洞信息主键集合
     * @return 结果
     */
    public int deleteCveInfoByCveIds(String[] cveIds);

    /**
     * 删除CVE漏洞信息信息
     * 
     * @param cveId CVE漏洞信息主键
     * @return 结果
     */
    public int deleteCveInfoByCveId(String cveId);

    /**
     * 根据组件查找相关CVE
     * 
     * @param component 组件标识 (groupId:artifactId)
     * @return CVE列表
     */
    public List<CveInfo> findCveByComponent(String component);

    /**
     * 批量插入CVE信息
     * 
     * @param cveInfoList CVE信息列表
     * @return 结果
     */
    public int batchInsertCveInfo(List<CveInfo> cveInfoList);

    /**
     * 同步CVE数据
     * 
     * @return 同步结果
     */
    public boolean syncCveData();
}
