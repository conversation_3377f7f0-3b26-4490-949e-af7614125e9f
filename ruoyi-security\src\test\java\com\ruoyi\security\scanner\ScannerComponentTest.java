package com.ruoyi.security.scanner;

import com.ruoyi.security.domain.ProjectDependency;
import com.ruoyi.security.scanner.GitScanner;
import com.ruoyi.security.scanner.MavenScanner;
import com.ruoyi.security.scanner.CveScanner;
import com.ruoyi.security.scanner.LicenseScanner;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 扫描器组件测试
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@SpringBootTest
@ActiveProfiles("test")
public class ScannerComponentTest {

    private GitScanner gitScanner;
    private MavenScanner mavenScanner;
    private CveScanner cveScanner;
    private LicenseScanner licenseScanner;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        gitScanner = new GitScanner();
        mavenScanner = new MavenScanner();
        cveScanner = new CveScanner();
        licenseScanner = new LicenseScanner();
    }

    @Test
    @DisplayName("测试Git扫描器 - 目录创建和清理")
    void testGitScannerDirectoryOperations() {
        String workspaceDir = tempDir.toString();
        String projectCode = "TEST001";
        
        // 测试目录创建
        File projectDir = gitScanner.createProjectDirectory(workspaceDir, projectCode);
        assertNotNull(projectDir, "项目目录应该创建成功");
        assertTrue(projectDir.exists(), "项目目录应该存在");
        assertTrue(projectDir.isDirectory(), "应该是一个目录");
        
        // 测试目录清理
        boolean cleaned = gitScanner.cleanupProjectDirectory(projectDir.getAbsolutePath());
        assertTrue(cleaned, "目录清理应该成功");
        assertFalse(projectDir.exists(), "清理后目录应该不存在");
    }

    @Test
    @DisplayName("测试Git扫描器 - 无效URL处理")
    void testGitScannerInvalidUrl() {
        String workspaceDir = tempDir.toString();
        String invalidGitUrl = "https://invalid-git-url.com/invalid/repo.git";
        String projectCode = "TEST002";
        
        // 测试无效Git URL
        String result = gitScanner.cloneRepository(invalidGitUrl, "master", 
                                                  null, null, workspaceDir, projectCode);
        assertNull(result, "无效Git URL应该返回null");
    }

    @Test
    @DisplayName("测试Maven扫描器 - 单模块项目")
    void testMavenScannerSingleModule() throws IOException {
        // 创建测试pom.xml文件
        File testPomFile = createTestPomFile(tempDir.toFile(), false);
        
        // 执行Maven扫描
        List<ProjectDependency> dependencies = mavenScanner.scanDependencies(
            testPomFile.getParent(), 1L);
        
        // 验证结果
        assertNotNull(dependencies, "依赖列表不应该为null");
        assertFalse(dependencies.isEmpty(), "应该扫描到依赖");
        
        // 验证依赖信息
        ProjectDependency firstDep = dependencies.get(0);
        assertNotNull(firstDep.getGroupId(), "GroupId不应该为空");
        assertNotNull(firstDep.getArtifactId(), "ArtifactId不应该为空");
        assertNotNull(firstDep.getVersion(), "Version不应该为空");
        assertEquals(1L, firstDep.getTaskId(), "TaskId应该匹配");
    }

    @Test
    @DisplayName("测试Maven扫描器 - 多模块项目")
    void testMavenScannerMultiModule() throws IOException {
        // 创建父pom.xml
        File parentDir = new File(tempDir.toFile(), "parent");
        parentDir.mkdirs();
        createTestPomFile(parentDir, true);
        
        // 创建子模块
        File moduleDir = new File(parentDir, "module1");
        moduleDir.mkdirs();
        createTestPomFile(moduleDir, false);
        
        // 执行Maven扫描
        List<ProjectDependency> dependencies = mavenScanner.scanDependencies(
            parentDir.getAbsolutePath(), 2L);
        
        // 验证结果
        assertNotNull(dependencies, "依赖列表不应该为null");
        // 多模块项目应该扫描到更多依赖
        assertTrue(dependencies.size() >= 1, "多模块项目应该扫描到依赖");
    }

    @Test
    @DisplayName("测试Maven扫描器 - 无效项目目录")
    void testMavenScannerInvalidDirectory() {
        String invalidDir = "/invalid/directory/path";
        
        // 测试无效目录
        List<ProjectDependency> dependencies = mavenScanner.scanDependencies(invalidDir, 3L);
        
        // 验证结果
        assertNotNull(dependencies, "依赖列表不应该为null");
        assertTrue(dependencies.isEmpty(), "无效目录应该返回空列表");
    }

    @Test
    @DisplayName("测试CVE扫描器 - 依赖匹配")
    void testCveScannerDependencyMatching() {
        // 创建测试依赖
        ProjectDependency testDep = new ProjectDependency();
        testDep.setGroupId("org.apache.commons");
        testDep.setArtifactId("commons-lang3");
        testDep.setVersion("3.8.1");
        
        // 执行CVE扫描
        cveScanner.scanDependency(testDep);
        
        // 验证结果（由于没有真实CVE数据，主要验证方法能正常执行）
        assertNotNull(testDep.getHasCve(), "HasCve字段应该被设置");
        assertTrue("0".equals(testDep.getHasCve()) || "1".equals(testDep.getHasCve()), 
                  "HasCve应该是0或1");
    }

    @Test
    @DisplayName("测试CVE扫描器 - CVSS评分计算")
    void testCveScannerCvssScoring() {
        // 测试不同CVSS评分的分级
        assertEquals("Critical", cveScanner.getCveSeverityLevel(9.5), "9.5分应该是Critical");
        assertEquals("High", cveScanner.getCveSeverityLevel(8.0), "8.0分应该是High");
        assertEquals("Medium", cveScanner.getCveSeverityLevel(5.5), "5.5分应该是Medium");
        assertEquals("Low", cveScanner.getCveSeverityLevel(2.0), "2.0分应该是Low");
        assertEquals("None", cveScanner.getCveSeverityLevel(0.0), "0.0分应该是None");
    }

    @Test
    @DisplayName("测试许可证扫描器 - 风险等级评估")
    void testLicenseScannerRiskAssessment() {
        // 测试高风险许可证
        assertEquals("3", licenseScanner.getLicenseRiskLevel("GPL-2.0"), "GPL-2.0应该是高风险");
        assertEquals("3", licenseScanner.getLicenseRiskLevel("GPL-3.0"), "GPL-3.0应该是高风险");
        assertEquals("3", licenseScanner.getLicenseRiskLevel("AGPL-3.0"), "AGPL-3.0应该是高风险");
        
        // 测试中风险许可证
        assertEquals("2", licenseScanner.getLicenseRiskLevel("LGPL-2.1"), "LGPL-2.1应该是中风险");
        assertEquals("2", licenseScanner.getLicenseRiskLevel("LGPL-3.0"), "LGPL-3.0应该是中风险");
        
        // 测试低风险许可证
        assertEquals("1", licenseScanner.getLicenseRiskLevel("Apache-2.0"), "Apache-2.0应该是低风险");
        assertEquals("1", licenseScanner.getLicenseRiskLevel("MIT"), "MIT应该是低风险");
        assertEquals("1", licenseScanner.getLicenseRiskLevel("BSD-3-Clause"), "BSD-3-Clause应该是低风险");
        
        // 测试未知许可证
        assertEquals("1", licenseScanner.getLicenseRiskLevel("Unknown-License"), "未知许可证应该是低风险");
    }

    @Test
    @DisplayName("测试许可证扫描器 - 依赖扫描")
    void testLicenseScannerDependencyScanning() {
        // 创建测试依赖
        ProjectDependency testDep = new ProjectDependency();
        testDep.setGroupId("org.apache.commons");
        testDep.setArtifactId("commons-lang3");
        testDep.setVersion("3.12.0");
        
        // 执行许可证扫描
        licenseScanner.scanDependency(testDep);
        
        // 验证结果（由于网络依赖，主要验证方法能正常执行）
        // 许可证信息可能为空（网络问题），但字段应该被初始化
        assertNotNull(testDep.getLicenseRiskLevel(), "许可证风险等级应该被设置");
    }

    @Test
    @DisplayName("测试扫描器异常处理")
    void testScannerExceptionHandling() {
        // 测试Git扫描器异常处理
        String result = gitScanner.cloneRepository(null, null, null, null, null, null);
        assertNull(result, "空参数应该返回null");
        
        // 测试Maven扫描器异常处理
        List<ProjectDependency> deps = mavenScanner.scanDependencies(null, 1L);
        assertNotNull(deps, "空参数应该返回空列表");
        assertTrue(deps.isEmpty(), "空参数应该返回空列表");
        
        // 测试CVE扫描器异常处理
        ProjectDependency nullDep = null;
        assertDoesNotThrow(() -> cveScanner.scanDependency(nullDep), 
                          "空依赖不应该抛出异常");
        
        // 测试许可证扫描器异常处理
        assertDoesNotThrow(() -> licenseScanner.scanDependency(nullDep), 
                          "空依赖不应该抛出异常");
    }

    /**
     * 创建测试用的pom.xml文件
     */
    private File createTestPomFile(File directory, boolean isParent) throws IOException {
        File pomFile = new File(directory, "pom.xml");
        
        try (FileWriter writer = new FileWriter(pomFile)) {
            writer.write("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n");
            writer.write("<project xmlns=\"http://maven.apache.org/POM/4.0.0\"\n");
            writer.write("         xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n");
            writer.write("         xsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\">\n");
            writer.write("    <modelVersion>4.0.0</modelVersion>\n");
            writer.write("    <groupId>com.test</groupId>\n");
            writer.write("    <artifactId>test-project</artifactId>\n");
            writer.write("    <version>1.0.0</version>\n");
            
            if (isParent) {
                writer.write("    <packaging>pom</packaging>\n");
                writer.write("    <modules>\n");
                writer.write("        <module>module1</module>\n");
                writer.write("    </modules>\n");
            }
            
            writer.write("    <dependencies>\n");
            writer.write("        <dependency>\n");
            writer.write("            <groupId>org.apache.commons</groupId>\n");
            writer.write("            <artifactId>commons-lang3</artifactId>\n");
            writer.write("            <version>3.12.0</version>\n");
            writer.write("        </dependency>\n");
            writer.write("        <dependency>\n");
            writer.write("            <groupId>junit</groupId>\n");
            writer.write("            <artifactId>junit</artifactId>\n");
            writer.write("            <version>4.13.2</version>\n");
            writer.write("            <scope>test</scope>\n");
            writer.write("        </dependency>\n");
            writer.write("    </dependencies>\n");
            writer.write("</project>\n");
        }
        
        return pomFile;
    }
}
