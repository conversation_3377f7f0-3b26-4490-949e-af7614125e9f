package com.ruoyi.security.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 项目依赖对象 project_dependency
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class ProjectDependency extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 依赖ID */
    private Long dependencyId;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Long projectId;

    /** 扫描任务ID */
    @Excel(name = "扫描任务ID")
    private Long taskId;

    /** Maven GroupId */
    @Excel(name = "Maven GroupId")
    private String groupId;

    /** Maven ArtifactId */
    @Excel(name = "Maven ArtifactId")
    private String artifactId;

    /** 版本号 */
    @Excel(name = "版本号")
    private String version;

    /** 许可证类型 */
    @Excel(name = "许可证类型")
    private String licenseType;

    /** 许可证风险等级(1低风险 2中风险 3高风险) */
    @Excel(name = "许可证风险等级", readConverterExp = "1=低风险,2=中风险,3=高风险")
    private String licenseRiskLevel;

    /** 是否存在CVE(0否 1是) */
    @Excel(name = "是否存在CVE", readConverterExp = "0=否,1=是")
    private String hasCve;

    /** CVE数量 */
    @Excel(name = "CVE数量")
    private Long cveCount;

    /** 最高CVSS评分 */
    @Excel(name = "最高CVSS评分")
    private BigDecimal maxCvssScore;

    /** CVE详情(JSON格式) */
    private String cveDetails;

    public void setDependencyId(Long dependencyId) 
    {
        this.dependencyId = dependencyId;
    }

    public Long getDependencyId() 
    {
        return dependencyId;
    }
    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }
    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }
    public void setGroupId(String groupId) 
    {
        this.groupId = groupId;
    }

    public String getGroupId() 
    {
        return groupId;
    }
    public void setArtifactId(String artifactId) 
    {
        this.artifactId = artifactId;
    }

    public String getArtifactId() 
    {
        return artifactId;
    }
    public void setVersion(String version) 
    {
        this.version = version;
    }

    public String getVersion() 
    {
        return version;
    }
    public void setLicenseType(String licenseType) 
    {
        this.licenseType = licenseType;
    }

    public String getLicenseType() 
    {
        return licenseType;
    }
    public void setLicenseRiskLevel(String licenseRiskLevel) 
    {
        this.licenseRiskLevel = licenseRiskLevel;
    }

    public String getLicenseRiskLevel() 
    {
        return licenseRiskLevel;
    }
    public void setHasCve(String hasCve) 
    {
        this.hasCve = hasCve;
    }

    public String getHasCve() 
    {
        return hasCve;
    }
    public void setCveCount(Long cveCount) 
    {
        this.cveCount = cveCount;
    }

    public Long getCveCount() 
    {
        return cveCount;
    }
    public void setMaxCvssScore(BigDecimal maxCvssScore) 
    {
        this.maxCvssScore = maxCvssScore;
    }

    public BigDecimal getMaxCvssScore() 
    {
        return maxCvssScore;
    }
    public void setCveDetails(String cveDetails) 
    {
        this.cveDetails = cveDetails;
    }

    public String getCveDetails() 
    {
        return cveDetails;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dependencyId", getDependencyId())
            .append("projectId", getProjectId())
            .append("taskId", getTaskId())
            .append("groupId", getGroupId())
            .append("artifactId", getArtifactId())
            .append("version", getVersion())
            .append("licenseType", getLicenseType())
            .append("licenseRiskLevel", getLicenseRiskLevel())
            .append("hasCve", getHasCve())
            .append("cveCount", getCveCount())
            .append("maxCvssScore", getMaxCvssScore())
            .append("cveDetails", getCveDetails())
            .append("createTime", getCreateTime())
            .toString();
    }
}
