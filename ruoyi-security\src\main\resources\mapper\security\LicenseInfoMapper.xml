<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.security.mapper.LicenseInfoMapper">
    
    <resultMap type="LicenseInfo" id="LicenseInfoResult">
        <result property="licenseId"        column="license_id"        />
        <result property="licenseName"      column="license_name"      />
        <result property="licenseType"      column="license_type"      />
        <result property="riskLevel"        column="risk_level"        />
        <result property="riskDescription"  column="risk_description"  />
        <result property="isCommercialUse"  column="is_commercial_use" />
        <result property="isModification"   column="is_modification"   />
        <result property="isDistribution"   column="is_distribution"   />
        <result property="licenseUrl"       column="license_url"       />
        <result property="licenseText"      column="license_text"      />
        <result property="status"           column="status"            />
        <result property="createBy"         column="create_by"         />
        <result property="createTime"       column="create_time"       />
        <result property="updateBy"         column="update_by"         />
        <result property="updateTime"       column="update_time"       />
        <result property="remark"           column="remark"            />
    </resultMap>

    <sql id="selectLicenseInfoVo">
        select license_id, license_name, license_type, risk_level, risk_description, is_commercial_use, is_modification, is_distribution, license_url, license_text, status, create_by, create_time, update_by, update_time, remark from license_info
    </sql>

    <select id="selectLicenseInfoList" parameterType="LicenseInfo" resultMap="LicenseInfoResult">
        <include refid="selectLicenseInfoVo"/>
        <where>  
            <if test="licenseName != null  and licenseName != ''"> and license_name like concat('%', #{licenseName}, '%')</if>
            <if test="licenseType != null  and licenseType != ''"> and license_type = #{licenseType}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectLicenseInfoByLicenseId" parameterType="Long" resultMap="LicenseInfoResult">
        <include refid="selectLicenseInfoVo"/>
        where license_id = #{licenseId}
    </select>

    <select id="selectLicenseInfoByType" parameterType="String" resultMap="LicenseInfoResult">
        <include refid="selectLicenseInfoVo"/>
        where license_type = #{licenseType} and status = '0'
        limit 1
    </select>

    <select id="checkLicenseNameUnique" parameterType="String" resultMap="LicenseInfoResult">
        <include refid="selectLicenseInfoVo"/>
        where license_name = #{licenseName}
        limit 1
    </select>
        
    <insert id="insertLicenseInfo" parameterType="LicenseInfo" useGeneratedKeys="true" keyProperty="licenseId">
        insert into license_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="licenseName != null and licenseName != ''">license_name,</if>
            <if test="licenseType != null">license_type,</if>
            <if test="riskLevel != null">risk_level,</if>
            <if test="riskDescription != null">risk_description,</if>
            <if test="isCommercialUse != null">is_commercial_use,</if>
            <if test="isModification != null">is_modification,</if>
            <if test="isDistribution != null">is_distribution,</if>
            <if test="licenseUrl != null">license_url,</if>
            <if test="licenseText != null">license_text,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="licenseName != null and licenseName != ''">#{licenseName},</if>
            <if test="licenseType != null">#{licenseType},</if>
            <if test="riskLevel != null">#{riskLevel},</if>
            <if test="riskDescription != null">#{riskDescription},</if>
            <if test="isCommercialUse != null">#{isCommercialUse},</if>
            <if test="isModification != null">#{isModification},</if>
            <if test="isDistribution != null">#{isDistribution},</if>
            <if test="licenseUrl != null">#{licenseUrl},</if>
            <if test="licenseText != null">#{licenseText},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateLicenseInfo" parameterType="LicenseInfo">
        update license_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="licenseName != null and licenseName != ''">license_name = #{licenseName},</if>
            <if test="licenseType != null">license_type = #{licenseType},</if>
            <if test="riskLevel != null">risk_level = #{riskLevel},</if>
            <if test="riskDescription != null">risk_description = #{riskDescription},</if>
            <if test="isCommercialUse != null">is_commercial_use = #{isCommercialUse},</if>
            <if test="isModification != null">is_modification = #{isModification},</if>
            <if test="isDistribution != null">is_distribution = #{isDistribution},</if>
            <if test="licenseUrl != null">license_url = #{licenseUrl},</if>
            <if test="licenseText != null">license_text = #{licenseText},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where license_id = #{licenseId}
    </update>

    <delete id="deleteLicenseInfoByLicenseId" parameterType="Long">
        delete from license_info where license_id = #{licenseId}
    </delete>

    <delete id="deleteLicenseInfoByLicenseIds" parameterType="String">
        delete from license_info where license_id in 
        <foreach item="licenseId" collection="array" open="(" separator="," close=")">
            #{licenseId}
        </foreach>
    </delete>

</mapper>
