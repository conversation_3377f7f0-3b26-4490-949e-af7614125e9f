package com.ruoyi.security.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 扫描任务对象 scan_task
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class ScanTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    private Long taskId;

    /** 项目ID */
    @Excel(name = "项目ID")
    private Long projectId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 任务状态(0待执行 1执行中 2已完成 3执行失败) */
    @Excel(name = "任务状态", readConverterExp = "0=待执行,1=执行中,2=已完成,3=执行失败")
    private String taskStatus;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 总依赖数 */
    @Excel(name = "总依赖数")
    private Long totalDependencies;

    /** 高危CVE数量 */
    @Excel(name = "高危CVE数量")
    private Long cveHighCount;

    /** 中危CVE数量 */
    @Excel(name = "中危CVE数量")
    private Long cveMediumCount;

    /** 低危CVE数量 */
    @Excel(name = "低危CVE数量")
    private Long cveLowCount;

    /** 高风险许可证数量 */
    @Excel(name = "高风险许可证数量")
    private Long licenseHighRiskCount;

    /** 中风险许可证数量 */
    @Excel(name = "中风险许可证数量")
    private Long licenseMediumRiskCount;

    /** 错误信息 */
    private String errorMsg;

    /** 项目名称 */
    private String projectName;

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }
    public void setProjectId(Long projectId) 
    {
        this.projectId = projectId;
    }

    public Long getProjectId() 
    {
        return projectId;
    }
    public void setTaskName(String taskName) 
    {
        this.taskName = taskName;
    }

    public String getTaskName() 
    {
        return taskName;
    }
    public void setTaskStatus(String taskStatus) 
    {
        this.taskStatus = taskStatus;
    }

    public String getTaskStatus() 
    {
        return taskStatus;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setTotalDependencies(Long totalDependencies) 
    {
        this.totalDependencies = totalDependencies;
    }

    public Long getTotalDependencies() 
    {
        return totalDependencies;
    }
    public void setCveHighCount(Long cveHighCount) 
    {
        this.cveHighCount = cveHighCount;
    }

    public Long getCveHighCount() 
    {
        return cveHighCount;
    }
    public void setCveMediumCount(Long cveMediumCount) 
    {
        this.cveMediumCount = cveMediumCount;
    }

    public Long getCveMediumCount() 
    {
        return cveMediumCount;
    }
    public void setCveLowCount(Long cveLowCount) 
    {
        this.cveLowCount = cveLowCount;
    }

    public Long getCveLowCount() 
    {
        return cveLowCount;
    }
    public void setLicenseHighRiskCount(Long licenseHighRiskCount) 
    {
        this.licenseHighRiskCount = licenseHighRiskCount;
    }

    public Long getLicenseHighRiskCount() 
    {
        return licenseHighRiskCount;
    }
    public void setLicenseMediumRiskCount(Long licenseMediumRiskCount) 
    {
        this.licenseMediumRiskCount = licenseMediumRiskCount;
    }

    public Long getLicenseMediumRiskCount() 
    {
        return licenseMediumRiskCount;
    }
    public void setErrorMsg(String errorMsg) 
    {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() 
    {
        return errorMsg;
    }

    public String getProjectName() 
    {
        return projectName;
    }

    public void setProjectName(String projectName) 
    {
        this.projectName = projectName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskId", getTaskId())
            .append("projectId", getProjectId())
            .append("taskName", getTaskName())
            .append("taskStatus", getTaskStatus())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("totalDependencies", getTotalDependencies())
            .append("cveHighCount", getCveHighCount())
            .append("cveMediumCount", getCveMediumCount())
            .append("cveLowCount", getCveLowCount())
            .append("licenseHighRiskCount", getLicenseHighRiskCount())
            .append("licenseMediumRiskCount", getLicenseMediumRiskCount())
            .append("errorMsg", getErrorMsg())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .toString();
    }
}
