# RuoYi 安全扫描模块

## 概述

RuoYi 安全扫描模块是基于 RuoYi 框架开发的企业级安全扫描管理系统，主要功能包括：

- 项目信息管理（支持GitLab认证）
- Maven依赖扫描（支持单模块和多模块项目）
- CVE漏洞检测
- 开源许可证风险评估
- 扫描报告生成和管理
- CVE数据同步

## 功能特性

### 1. 项目管理
- 支持GitLab项目管理
- Git连接测试功能
- 项目扫描状态跟踪
- 支持用户名密码认证

### 2. 扫描引擎
- **Git代码拉取**：自动克隆GitLab仓库
- **Maven依赖解析**：支持单模块和多模块Maven项目
- **CVE漏洞扫描**：基于NVD数据库进行漏洞匹配
- **许可证检查**：重点关注GPL/AGPL等传染性许可证

### 3. 风险评估
- **CVE风险等级**：Critical、High、Medium、Low
- **许可证风险等级**：高风险（GPL/AGPL）、中风险（LGPL）、低风险（Apache/MIT/BSD）
- **CVSS评分**：支持CVSS 3.1评分标准

### 4. 报告管理
- 详细的扫描报告生成
- 支持按项目名模糊查询
- JSON格式存储报告内容
- 统计信息汇总

### 5. 数据管理
- CVE数据同步（手动触发）
- 字典数据管理（风险等级、许可证类型等）
- 扫描历史记录

## 技术架构

### 核心组件
- **GitScanner**：Git仓库操作
- **MavenScanner**：Maven依赖解析
- **CveScanner**：CVE漏洞检测
- **LicenseScanner**：许可证风险评估
- **ScanService**：扫描流程编排

### 数据库设计
- `project_info`：项目信息表
- `scan_task`：扫描任务表
- `cve_info`：CVE漏洞信息表
- `project_dependency`：项目依赖表
- `scan_report`：扫描报告表

### 依赖库
- **JGit 6.7.0**：Git操作
- **Maven Model 3.9.4**：Maven POM解析
- **HttpClient 4.5.14**：HTTP请求
- **Jackson**：JSON处理

## 安装配置

### 1. 数据库初始化
执行 `sql/security_scan.sql` 脚本创建数据表和初始化字典数据。

### 2. 配置文件
在 `application.yml` 中添加配置：

```yaml
ruoyi:
  security:
    scan:
      # 扫描工作目录
      workspace: D:/ruoyi/security-scan
```

### 3. 权限配置
需要配置以下权限：
- `security:project:*` - 项目管理权限
- `security:scan:*` - 扫描管理权限
- `security:report:*` - 报告管理权限
- `security:cve:*` - CVE管理权限
- `security:dashboard:*` - 仪表板权限

## API接口

### 项目管理
- `GET /security/project/list` - 查询项目列表
- `POST /security/project` - 新增项目
- `PUT /security/project` - 修改项目
- `DELETE /security/project/{ids}` - 删除项目
- `POST /security/project/testConnection` - 测试Git连接

### 扫描管理
- `POST /security/scan/execute/{projectId}` - 执行扫描
- `GET /security/scan/status/{taskId}` - 获取扫描状态
- `POST /security/scan/stop/{taskId}` - 停止扫描
- `GET /security/scan/task/list` - 查询扫描任务列表

### 报告管理
- `GET /security/report/list` - 查询报告列表
- `GET /security/report/search?projectName=xxx` - 按项目名搜索
- `GET /security/report/{reportId}` - 获取报告详情
- `GET /security/report/content/{reportId}` - 获取报告内容

### CVE管理
- `GET /security/cve/list` - 查询CVE列表
- `POST /security/cve/sync` - 同步CVE数据
- `GET /security/cve/component/{component}` - 按组件查询CVE

### 仪表板
- `GET /security/dashboard/overview` - 获取概览数据
- `GET /security/dashboard/recent-tasks` - 获取最近任务
- `GET /security/dashboard/recent-reports` - 获取最近报告

## 使用流程

### 1. 添加项目
1. 进入项目管理页面
2. 点击"新增"按钮
3. 填写项目信息（项目名称、编码、Git地址、分支、认证信息）
4. 点击"测试连接"验证Git配置
5. 保存项目

### 2. 执行扫描
1. 在项目列表中选择要扫描的项目
2. 点击"执行扫描"按钮
3. 系统自动执行以下步骤：
   - 克隆Git仓库
   - 解析Maven依赖
   - 检查CVE漏洞
   - 评估许可证风险
   - 生成扫描报告

### 3. 查看报告
1. 进入扫描报告页面
2. 可按项目名称模糊搜索
3. 点击报告查看详细内容
4. 报告包含：
   - 项目基本信息
   - 扫描统计数据
   - 高风险依赖列表
   - 所有依赖详情

### 4. CVE数据管理
1. 进入CVE管理页面
2. 点击"同步数据"从NVD获取最新CVE信息
3. 查看和管理CVE数据

## 注意事项

### 1. 系统要求
- Java 8+
- Maven 3.6+
- MySQL 5.7+
- 足够的磁盘空间用于代码克隆

### 2. 网络要求
- 能够访问GitLab服务器
- 能够访问NVD API（https://services.nvd.nist.gov）
- 能够访问Maven中央仓库API

### 3. 性能考虑
- 大型项目扫描可能需要较长时间
- 建议配置足够的工作目录空间
- CVE数据同步建议在非业务高峰期进行

### 4. 安全考虑
- Git认证信息加密存储
- 扫描完成后自动清理本地代码
- 建议定期备份扫描报告数据

## 扩展开发

### 1. 添加新的扫描器
实现 `Scanner` 接口并在 `ScanService` 中集成。

### 2. 自定义报告格式
修改 `ScanReportService.buildReportContent()` 方法。

### 3. 集成其他CVE数据源
扩展 `CveInfoService.syncCveData()` 方法。

### 4. 添加新的许可证类型
在 `LicenseScanner.LICENSE_RISK_MAP` 中添加映射关系。

## 故障排除

### 1. Git连接失败
- 检查Git地址是否正确
- 验证认证信息
- 确认网络连通性

### 2. Maven解析失败
- 确认项目包含pom.xml文件
- 检查pom.xml格式是否正确

### 3. CVE同步失败
- 检查网络连接
- 验证NVD API可访问性
- 查看日志获取详细错误信息

### 4. 扫描任务卡住
- 检查系统资源使用情况
- 查看扫描日志
- 必要时重启扫描服务

## 版本历史

- **v1.0.0** - 初始版本，包含基础扫描功能
- 支持GitLab项目管理
- 支持Maven依赖扫描
- 支持CVE漏洞检测
- 支持许可证风险评估
- 支持扫描报告生成

## 联系支持

如有问题或建议，请联系开发团队。
