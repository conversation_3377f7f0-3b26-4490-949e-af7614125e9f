package com.ruoyi.security.scanner;

import java.math.BigDecimal;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.security.domain.CveInfo;
import com.ruoyi.security.domain.ProjectDependency;
import com.ruoyi.security.service.ICveInfoService;

/**
 * CVE扫描器
 * 负责检查Maven依赖的CVE漏洞
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Component
public class CveScanner
{
    private static final Logger log = LoggerFactory.getLogger(CveScanner.class);

    @Autowired
    private ICveInfoService cveInfoService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 扫描依赖的CVE漏洞
     * 
     * @param dependencies 依赖列表
     */
    public void scanCveVulnerabilities(List<ProjectDependency> dependencies)
    {
        for (ProjectDependency dependency : dependencies)
        {
            try
            {
                scanSingleDependency(dependency);
            }
            catch (Exception e)
            {
                log.error("扫描依赖CVE失败: {}:{}", dependency.getGroupId(), dependency.getArtifactId(), e);
            }
        }
    }

    /**
     * 扫描单个依赖的CVE漏洞
     * 
     * @param dependency 依赖
     */
    private void scanSingleDependency(ProjectDependency dependency)
    {
        // 构建搜索条件
        String searchKey = dependency.getGroupId() + ":" + dependency.getArtifactId();
        
        // 从数据库查询相关CVE
        List<CveInfo> cveList = cveInfoService.findCveByComponent(searchKey);
        
        if (cveList != null && !cveList.isEmpty())
        {
            dependency.setHasCve("1");
            dependency.setCveCount((long) cveList.size());
            
            // 计算最高CVSS评分和统计各等级CVE数量
            BigDecimal maxCvssScore = BigDecimal.ZERO;
            int highCount = 0, mediumCount = 0, lowCount = 0, criticalCount = 0;
            
            for (CveInfo cve : cveList)
            {
                // 检查版本是否受影响
                if (isVersionAffected(dependency.getVersion(), cve))
                {
                    if (cve.getCvssScore() != null && cve.getCvssScore().compareTo(maxCvssScore) > 0)
                    {
                        maxCvssScore = cve.getCvssScore();
                    }
                    
                    // 统计各等级数量
                    String severity = cve.getSeverityLevel();
                    switch (severity)
                    {
                        case "1": lowCount++; break;
                        case "2": mediumCount++; break;
                        case "3": highCount++; break;
                        case "4": criticalCount++; break;
                    }
                }
            }
            
            dependency.setMaxCvssScore(maxCvssScore);
            
            // 构建CVE详情JSON
            try
            {
                CveDetails details = new CveDetails();
                details.setCriticalCount(criticalCount);
                details.setHighCount(highCount);
                details.setMediumCount(mediumCount);
                details.setLowCount(lowCount);
                details.setCveList(cveList);
                
                String cveDetailsJson = objectMapper.writeValueAsString(details);
                dependency.setCveDetails(cveDetailsJson);
            }
            catch (Exception e)
            {
                log.error("构建CVE详情JSON失败", e);
            }
        }
        else
        {
            dependency.setHasCve("0");
            dependency.setCveCount(0L);
            dependency.setMaxCvssScore(BigDecimal.ZERO);
        }
    }

    /**
     * 检查版本是否受CVE影响
     * 简化版本比较，实际应该使用语义化版本比较
     * 
     * @param version 依赖版本
     * @param cve CVE信息
     * @return 是否受影响
     */
    private boolean isVersionAffected(String version, CveInfo cve)
    {
        // 简化实现：如果CVE的影响组件包含该依赖，则认为受影响
        // 实际应该解析affected_components JSON并进行版本范围比较
        try
        {
            String affectedComponents = cve.getAffectedComponents();
            if (affectedComponents != null && affectedComponents.contains(version))
            {
                return true;
            }
        }
        catch (Exception e)
        {
            log.debug("解析影响组件失败", e);
        }
        
        // 默认认为受影响（保守策略）
        return true;
    }

    /**
     * CVE详情内部类
     */
    public static class CveDetails
    {
        private int criticalCount;
        private int highCount;
        private int mediumCount;
        private int lowCount;
        private List<CveInfo> cveList;

        // Getters and Setters
        public int getCriticalCount() { return criticalCount; }
        public void setCriticalCount(int criticalCount) { this.criticalCount = criticalCount; }
        
        public int getHighCount() { return highCount; }
        public void setHighCount(int highCount) { this.highCount = highCount; }
        
        public int getMediumCount() { return mediumCount; }
        public void setMediumCount(int mediumCount) { this.mediumCount = mediumCount; }
        
        public int getLowCount() { return lowCount; }
        public void setLowCount(int lowCount) { this.lowCount = lowCount; }
        
        public List<CveInfo> getCveList() { return cveList; }
        public void setCveList(List<CveInfo> cveList) { this.cveList = cveList; }
    }
}
