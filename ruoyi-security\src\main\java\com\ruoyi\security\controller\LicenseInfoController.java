package com.ruoyi.security.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.security.domain.LicenseInfo;
import com.ruoyi.security.service.ILicenseInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 许可证信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/security/license")
public class LicenseInfoController extends BaseController
{
    @Autowired
    private ILicenseInfoService licenseInfoService;

    /**
     * 查询许可证信息列表
     */
    @PreAuthorize("@ss.hasPermi('security:license:list')")
    @GetMapping("/list")
    public TableDataInfo list(LicenseInfo licenseInfo)
    {
        startPage();
        List<LicenseInfo> list = licenseInfoService.selectLicenseInfoList(licenseInfo);
        return getDataTable(list);
    }

    /**
     * 导出许可证信息列表
     */
    @PreAuthorize("@ss.hasPermi('security:license:export')")
    @Log(title = "许可证信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LicenseInfo licenseInfo)
    {
        List<LicenseInfo> list = licenseInfoService.selectLicenseInfoList(licenseInfo);
        ExcelUtil<LicenseInfo> util = new ExcelUtil<LicenseInfo>(LicenseInfo.class);
        util.exportExcel(response, list, "许可证信息数据");
    }

    /**
     * 获取许可证信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('security:license:query')")
    @GetMapping(value = "/{licenseId}")
    public AjaxResult getInfo(@PathVariable("licenseId") Long licenseId)
    {
        return success(licenseInfoService.selectLicenseInfoByLicenseId(licenseId));
    }

    /**
     * 新增许可证信息
     */
    @PreAuthorize("@ss.hasPermi('security:license:add')")
    @Log(title = "许可证信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LicenseInfo licenseInfo)
    {
        if (!licenseInfoService.checkLicenseNameUnique(licenseInfo))
        {
            return error("新增许可证'" + licenseInfo.getLicenseName() + "'失败，许可证名称已存在");
        }
        return toAjax(licenseInfoService.insertLicenseInfo(licenseInfo));
    }

    /**
     * 修改许可证信息
     */
    @PreAuthorize("@ss.hasPermi('security:license:edit')")
    @Log(title = "许可证信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LicenseInfo licenseInfo)
    {
        if (!licenseInfoService.checkLicenseNameUnique(licenseInfo))
        {
            return error("修改许可证'" + licenseInfo.getLicenseName() + "'失败，许可证名称已存在");
        }
        return toAjax(licenseInfoService.updateLicenseInfo(licenseInfo));
    }

    /**
     * 删除许可证信息
     */
    @PreAuthorize("@ss.hasPermi('security:license:remove')")
    @Log(title = "许可证信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{licenseIds}")
    public AjaxResult remove(@PathVariable Long[] licenseIds)
    {
        return toAjax(licenseInfoService.deleteLicenseInfoByLicenseIds(licenseIds));
    }

    /**
     * 根据许可证类型查询许可证信息
     */
    @PreAuthorize("@ss.hasPermi('security:license:query')")
    @GetMapping("/type/{licenseType}")
    public AjaxResult getByType(@PathVariable("licenseType") String licenseType)
    {
        LicenseInfo licenseInfo = licenseInfoService.selectLicenseInfoByType(licenseType);
        if (licenseInfo != null)
        {
            return success(licenseInfo);
        }
        return error("许可证类型不存在");
    }

    /**
     * 校验许可证名称
     */
    @GetMapping("/checkLicenseNameUnique")
    public AjaxResult checkLicenseNameUnique(LicenseInfo licenseInfo)
    {
        return success(licenseInfoService.checkLicenseNameUnique(licenseInfo));
    }
}
