-- 创建测试数据库初始化脚本
-- 注意：此脚本需要在运行测试前手动执行，或通过CI/CD流程自动执行

-- 创建测试数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS ry_test DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 使用测试数据库
USE ry_test;

-- 删除已存在的表（按依赖关系顺序）
DROP TABLE IF EXISTS scan_report;
DROP TABLE IF EXISTS project_dependency;
DROP TABLE IF EXISTS scan_task;
DROP TABLE IF EXISTS project_info;
DROP TABLE IF EXISTS cve_info;
DROP TABLE IF EXISTS sys_dict_data;
DROP TABLE IF EXISTS sys_dict_type;

-- 创建字典类型表
CREATE TABLE sys_dict_type (
    dict_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典主键',
    dict_name varchar(100) DEFAULT '' COMMENT '字典名称',
    dict_type varchar(100) DEFAULT '' COMMENT '字典类型',
    status char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by varchar(64) DEFAULT '' COMMENT '创建者',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_by varchar(64) DEFAULT '' COMMENT '更新者',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (dict_id),
    UNIQUE KEY uk_dict_type (dict_type)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='字典类型表';

-- 创建字典数据表
CREATE TABLE sys_dict_data (
    dict_code bigint(20) NOT NULL AUTO_INCREMENT COMMENT '字典编码',
    dict_sort int(4) DEFAULT 0 COMMENT '字典排序',
    dict_label varchar(100) DEFAULT '' COMMENT '字典标签',
    dict_value varchar(100) DEFAULT '' COMMENT '字典键值',
    dict_type varchar(100) DEFAULT '' COMMENT '字典类型',
    css_class varchar(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
    list_class varchar(100) DEFAULT NULL COMMENT '表格回显样式',
    is_default char(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
    status char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    create_by varchar(64) DEFAULT '' COMMENT '创建者',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_by varchar(64) DEFAULT '' COMMENT '更新者',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (dict_code)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='字典数据表';

-- 创建项目信息表
CREATE TABLE project_info (
    project_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '项目ID',
    project_name varchar(100) NOT NULL COMMENT '项目名称',
    project_code varchar(50) NOT NULL COMMENT '项目编码',
    git_url varchar(500) NOT NULL COMMENT 'Git地址',
    git_branch varchar(100) DEFAULT 'master' COMMENT 'Git分支',
    git_username varchar(100) DEFAULT NULL COMMENT 'Git用户名',
    git_password varchar(200) DEFAULT NULL COMMENT 'Git密码',
    project_desc varchar(500) DEFAULT NULL COMMENT '项目描述',
    project_owner varchar(50) DEFAULT NULL COMMENT '项目负责人',
    status char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
    scan_status char(1) DEFAULT '0' COMMENT '扫描状态（0待扫描 1扫描中 2已完成 3扫描失败）',
    last_scan_time datetime DEFAULT NULL COMMENT '最后扫描时间',
    del_flag char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    create_by varchar(64) DEFAULT '' COMMENT '创建者',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_by varchar(64) DEFAULT '' COMMENT '更新者',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (project_id),
    UNIQUE KEY uk_project_code (project_code),
    KEY idx_project_name (project_name),
    KEY idx_scan_status (scan_status)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='项目信息表';

-- 创建扫描任务表
CREATE TABLE scan_task (
    task_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    project_id bigint(20) NOT NULL COMMENT '项目ID',
    task_name varchar(100) NOT NULL COMMENT '任务名称',
    task_status char(1) DEFAULT '0' COMMENT '任务状态（0待执行 1执行中 2已完成 3执行失败）',
    start_time datetime DEFAULT NULL COMMENT '开始时间',
    end_time datetime DEFAULT NULL COMMENT '结束时间',
    error_message text DEFAULT NULL COMMENT '错误信息',
    del_flag char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    create_by varchar(64) DEFAULT '' COMMENT '创建者',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_by varchar(64) DEFAULT '' COMMENT '更新者',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (task_id),
    KEY idx_project_id (project_id),
    KEY idx_task_status (task_status),
    CONSTRAINT fk_scan_task_project FOREIGN KEY (project_id) REFERENCES project_info (project_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='扫描任务表';

-- 创建CVE信息表
CREATE TABLE cve_info (
    cve_id varchar(50) NOT NULL COMMENT 'CVE编号',
    description text DEFAULT NULL COMMENT 'CVE描述',
    cvss_score decimal(3,1) DEFAULT NULL COMMENT 'CVSS评分',
    severity_level varchar(20) DEFAULT NULL COMMENT '严重等级',
    published_date date DEFAULT NULL COMMENT '发布日期',
    last_modified_date date DEFAULT NULL COMMENT '最后修改日期',
    del_flag char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    create_by varchar(64) DEFAULT '' COMMENT '创建者',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_by varchar(64) DEFAULT '' COMMENT '更新者',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (cve_id),
    UNIQUE KEY uk_cve_id (cve_id),
    KEY idx_severity_level (severity_level),
    KEY idx_published_date (published_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CVE信息表';

-- 创建项目依赖表
CREATE TABLE project_dependency (
    dependency_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '依赖ID',
    task_id bigint(20) NOT NULL COMMENT '任务ID',
    group_id varchar(200) NOT NULL COMMENT '组织ID',
    artifact_id varchar(200) NOT NULL COMMENT '构件ID',
    version varchar(50) NOT NULL COMMENT '版本号',
    scope varchar(20) DEFAULT 'compile' COMMENT '作用域',
    has_cve char(1) DEFAULT '0' COMMENT '是否有CVE（0否 1是）',
    cve_count bigint(20) DEFAULT 0 COMMENT 'CVE数量',
    max_cvss_score decimal(3,1) DEFAULT NULL COMMENT '最高CVSS评分',
    license_name varchar(100) DEFAULT NULL COMMENT '许可证名称',
    license_risk_level char(1) DEFAULT '1' COMMENT '许可证风险等级（1低风险 2中风险 3高风险）',
    del_flag char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    create_by varchar(64) DEFAULT '' COMMENT '创建者',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_by varchar(64) DEFAULT '' COMMENT '更新者',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (dependency_id),
    KEY idx_task_id (task_id),
    KEY idx_group_artifact (group_id, artifact_id),
    KEY idx_has_cve (has_cve),
    KEY idx_license_risk (license_risk_level),
    CONSTRAINT fk_project_dependency_task FOREIGN KEY (task_id) REFERENCES scan_task (task_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='项目依赖表';

-- 创建扫描报告表
CREATE TABLE scan_report (
    report_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '报告ID',
    task_id bigint(20) NOT NULL COMMENT '任务ID',
    project_id bigint(20) NOT NULL COMMENT '项目ID',
    project_name varchar(100) NOT NULL COMMENT '项目名称',
    project_code varchar(50) NOT NULL COMMENT '项目编码',
    scan_time datetime NOT NULL COMMENT '扫描时间',
    total_dependencies bigint(20) DEFAULT 0 COMMENT '总依赖数',
    cve_dependencies bigint(20) DEFAULT 0 COMMENT '有CVE的依赖数',
    high_risk_dependencies bigint(20) DEFAULT 0 COMMENT '高风险依赖数',
    medium_risk_dependencies bigint(20) DEFAULT 0 COMMENT '中风险依赖数',
    low_risk_dependencies bigint(20) DEFAULT 0 COMMENT '低风险依赖数',
    license_risk_dependencies bigint(20) DEFAULT 0 COMMENT '许可证风险依赖数',
    report_content longtext DEFAULT NULL COMMENT '报告内容（JSON格式）',
    del_flag char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    create_by varchar(64) DEFAULT '' COMMENT '创建者',
    create_time datetime DEFAULT NULL COMMENT '创建时间',
    update_by varchar(64) DEFAULT '' COMMENT '更新者',
    update_time datetime DEFAULT NULL COMMENT '更新时间',
    remark varchar(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (report_id),
    KEY idx_task_id (task_id),
    KEY idx_project_id (project_id),
    KEY idx_project_name (project_name),
    KEY idx_scan_time (scan_time),
    CONSTRAINT fk_scan_report_task FOREIGN KEY (task_id) REFERENCES scan_task (task_id),
    CONSTRAINT fk_scan_report_project FOREIGN KEY (project_id) REFERENCES project_info (project_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='扫描报告表';

-- 插入基础字典数据
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) VALUES 
('许可证风险等级', 'license_risk_level', '0', 'admin', NOW(), '许可证风险等级字典'),
('CVE严重等级', 'cve_severity_level', '0', 'admin', NOW(), 'CVE严重等级字典'),
('扫描状态', 'scan_status', '0', 'admin', NOW(), '扫描状态字典');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES 
(1, '低风险', '1', 'license_risk_level', '', 'success', 'Y', '0', 'admin', NOW(), '低风险许可证'),
(2, '中风险', '2', 'license_risk_level', '', 'warning', 'N', '0', 'admin', NOW(), '中风险许可证'),
(3, '高风险', '3', 'license_risk_level', '', 'danger', 'N', '0', 'admin', NOW(), '高风险许可证'),
(1, '无', 'None', 'cve_severity_level', '', 'info', 'Y', '0', 'admin', NOW(), '无CVE'),
(2, '低', 'Low', 'cve_severity_level', '', 'success', 'N', '0', 'admin', NOW(), '低危CVE'),
(3, '中', 'Medium', 'cve_severity_level', '', 'warning', 'N', '0', 'admin', NOW(), '中危CVE'),
(4, '高', 'High', 'cve_severity_level', '', 'danger', 'N', '0', 'admin', NOW(), '高危CVE'),
(5, '严重', 'Critical', 'cve_severity_level', '', 'danger', 'N', '0', 'admin', NOW(), '严重CVE'),
(1, '待扫描', '0', 'scan_status', '', 'info', 'Y', '0', 'admin', NOW(), '待扫描状态'),
(2, '扫描中', '1', 'scan_status', '', 'warning', 'N', '0', 'admin', NOW(), '扫描中状态'),
(3, '已完成', '2', 'scan_status', '', 'success', 'N', '0', 'admin', NOW(), '扫描完成状态'),
(4, '扫描失败', '3', 'scan_status', '', 'danger', 'N', '0', 'admin', NOW(), '扫描失败状态');

COMMIT;
