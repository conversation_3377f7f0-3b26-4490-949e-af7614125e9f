-- 安全扫描模块菜单 SQL
-- 一级菜单：安全扫描
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('安全扫描', '0', '5', '#', 'M', '0', '', 'fa fa-shield', 'admin', sysdate(), '', null, '安全扫描目录');

-- 获取刚插入的安全扫描菜单ID
SELECT @securityMenuId := LAST_INSERT_ID();

-- 二级菜单：项目管理
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('项目管理', @securityMenuId, '1', '/security/project', 'C', '0', 'security:project:view', 'fa fa-folder-open', 'admin', sysdate(), '', null, '项目管理菜单');

-- 获取项目管理菜单ID
SELECT @projectMenuId := LAST_INSERT_ID();

-- 项目管理按钮权限
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('项目查询', @projectMenuId, '1', '#', 'F', '0', 'security:project:list', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('项目新增', @projectMenuId, '2', '#', 'F', '0', 'security:project:add', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('项目修改', @projectMenuId, '3', '#', 'F', '0', 'security:project:edit', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('项目删除', @projectMenuId, '4', '#', 'F', '0', 'security:project:remove', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('项目导出', @projectMenuId, '5', '#', 'F', '0', 'security:project:export', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('连接测试', @projectMenuId, '6', '#', 'F', '0', 'security:project:test', '#', 'admin', sysdate(), '', null, '');

-- 二级菜单：扫描管理
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('扫描管理', @securityMenuId, '2', '/security/scan', 'C', '0', 'security:scan:view', 'fa fa-search', 'admin', sysdate(), '', null, '扫描管理菜单');

-- 获取扫描管理菜单ID
SELECT @scanMenuId := LAST_INSERT_ID();

-- 扫描管理按钮权限
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('扫描查询', @scanMenuId, '1', '#', 'F', '0', 'security:scan:list', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('开始扫描', @scanMenuId, '2', '#', 'F', '0', 'security:scan:start', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('停止扫描', @scanMenuId, '3', '#', 'F', '0', 'security:scan:stop', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('扫描导出', @scanMenuId, '4', '#', 'F', '0', 'security:scan:export', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('查看报告', @scanMenuId, '5', '#', 'F', '0', 'security:scan:report', '#', 'admin', sysdate(), '', null, '');

-- 二级菜单：扫描报告
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('扫描报告', @securityMenuId, '3', '/security/report', 'C', '0', 'security:report:view', 'fa fa-file-text-o', 'admin', sysdate(), '', null, '扫描报告菜单');

-- 获取扫描报告菜单ID
SELECT @reportMenuId := LAST_INSERT_ID();

-- 扫描报告按钮权限
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('报告查询', @reportMenuId, '1', '#', 'F', '0', 'security:report:list', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('报告详情', @reportMenuId, '2', '#', 'F', '0', 'security:report:detail', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('报告导出', @reportMenuId, '3', '#', 'F', '0', 'security:report:export', '#', 'admin', sysdate(), '', null, '');

-- 二级菜单：CVE管理
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('CVE管理', @securityMenuId, '4', '/security/cve', 'C', '0', 'security:cve:view', 'fa fa-bug', 'admin', sysdate(), '', null, 'CVE管理菜单');

-- 获取CVE管理菜单ID
SELECT @cveMenuId := LAST_INSERT_ID();

-- CVE管理按钮权限
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('CVE查询', @cveMenuId, '1', '#', 'F', '0', 'security:cve:list', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('CVE同步', @cveMenuId, '2', '#', 'F', '0', 'security:cve:sync', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('CVE导出', @cveMenuId, '3', '#', 'F', '0', 'security:cve:export', '#', 'admin', sysdate(), '', null, '');

-- 二级菜单：许可证管理
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('许可证管理', @securityMenuId, '5', '/security/license', 'C', '0', 'security:license:view', 'fa fa-legal', 'admin', sysdate(), '', null, '许可证管理菜单');

-- 获取许可证管理菜单ID
SELECT @licenseMenuId := LAST_INSERT_ID();

-- 许可证管理按钮权限
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('许可证查询', @licenseMenuId, '1', '#', 'F', '0', 'security:license:list', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('许可证新增', @licenseMenuId, '2', '#', 'F', '0', 'security:license:add', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('许可证修改', @licenseMenuId, '3', '#', 'F', '0', 'security:license:edit', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('许可证删除', @licenseMenuId, '4', '#', 'F', '0', 'security:license:remove', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('许可证导出', @licenseMenuId, '5', '#', 'F', '0', 'security:license:export', '#', 'admin', sysdate(), '', null, '');

-- 二级菜单：安全仪表板
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('安全仪表板', @securityMenuId, '6', '/security/dashboard', 'C', '0', 'security:dashboard:view', 'fa fa-dashboard', 'admin', sysdate(), '', null, '安全仪表板菜单');

-- 获取安全仪表板菜单ID
SELECT @dashboardMenuId := LAST_INSERT_ID();

-- 安全仪表板按钮权限
insert into sys_menu (menu_name, parent_id, order_num, url, menu_type, visible, perms, icon, create_by, create_time, update_by, update_time, remark)
values('仪表板查询', @dashboardMenuId, '1', '#', 'F', '0', 'security:dashboard:list', '#', 'admin', sysdate(), '', null, '');

-- 为管理员角色分配安全扫描模块的所有权限
-- 获取管理员角色ID (通常是1)
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'security:%' OR (parent_id = @securityMenuId AND menu_type = 'M');

-- 插入安全扫描主菜单权限
INSERT INTO sys_role_menu (role_id, menu_id) VALUES (1, @securityMenuId);
