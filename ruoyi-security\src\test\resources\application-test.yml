# 测试环境配置
server:
  port: 8081

spring:
  # 数据源配置 - 使用H2内存数据库
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
    username: sa
    password: 
    hikari:
      minimum-idle: 1
      maximum-pool-size: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true

  # H2控制台配置（测试时可用）
  h2:
    console:
      enabled: true
      path: /h2-console

  # Redis配置 - 使用嵌入式Redis
  redis:
    host: localhost
    port: 6379
    database: 1
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms

  # 缓存配置
  cache:
    type: simple

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper: 
  helperDialect: h2
  supportMethodsArguments: true
  params: count=countSql 

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss: 
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework.security: debug
    org.springframework.web: debug
    org.mybatis: debug
    com.ruoyi.security: debug
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 安全扫描配置
security:
  scan:
    # 工作目录
    workspace: ${java.io.tmpdir}/security-scan-test
    # Git配置
    git:
      timeout: 30
      max-retries: 3
    # Maven配置  
    maven:
      timeout: 300
      max-depth: 10
    # CVE配置
    cve:
      api-url: https://services.nvd.nist.gov/rest/json/cves/2.0
      timeout: 30
      batch-size: 100
    # 许可证配置
    license:
      timeout: 30
      cache-enabled: true

# 测试专用配置
test:
  # 是否启用网络测试（需要外网连接）
  network-enabled: false
  # 测试数据目录
  data-dir: src/test/resources/testdata
  # Mock配置
  mock:
    git-enabled: true
    cve-enabled: true
    license-enabled: true

# RuoYi配置
ruoyi:
  # 名称
  name: RuoYi-Security-Test
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2025
  # 实例演示开关
  demoEnabled: false
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: ${java.io.tmpdir}/ruoyi-test
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring Security配置
security:
  # 测试环境关闭CSRF
  csrf:
    enabled: false
  # 测试用户配置
  user:
    name: admin
    password: admin123
    roles: ADMIN
