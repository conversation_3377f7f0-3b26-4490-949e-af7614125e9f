# RuoYi 安全扫描模块功能验证指南

## 访问应用

1. **启动应用**
   - 应用已正常启动，运行在 http://localhost:8080

2. **登录系统**
   - 用户名：`admin`
   - 密码：`admin123`

## 验证步骤

### 1. 验证模块菜单
登录后检查左侧菜单是否包含"安全扫描"相关菜单项：
- 项目管理
- 扫描管理  
- 扫描报告
- CVE管理
- 许可证管理
- 仪表板

### 2. 项目管理功能验证
导航到"项目管理"页面：
- [ ] 页面正常加载
- [ ] 可以添加新项目
- [ ] 可以编辑项目信息
- [ ] Git连接测试功能正常
- [ ] 项目列表显示正确

**测试数据示例：**
```
项目名称: 测试项目
Git地址: https://github.com/spring-projects/spring-boot.git
分支: main
描述: 用于测试的Spring Boot项目
```

### 3. 扫描管理功能验证
导航到"扫描管理"页面：
- [ ] 可以创建新的扫描任务
- [ ] 扫描任务列表显示正确
- [ ] 可以启动扫描任务
- [ ] 扫描状态更新正常

### 4. 数据库验证
检查MySQL数据库中的表是否正确创建：
```sql
-- 检查表是否存在
SHOW TABLES LIKE 'sec_%';

-- 检查项目信息表
SELECT * FROM sec_project_info LIMIT 5;

-- 检查CVE信息表
SELECT * FROM sec_cve_info LIMIT 5;

-- 检查许可证信息表  
SELECT * FROM sec_license_info LIMIT 5;
```

### 5. API接口验证
可以使用以下curl命令测试API接口：

```bash
# 获取项目列表
curl -X GET "http://localhost:8080/security/project/list" \
  -H "Cookie: Admin-Token=YOUR_TOKEN"

# 测试Git连接
curl -X POST "http://localhost:8080/security/project/testConnection" \
  -H "Content-Type: application/json" \
  -H "Cookie: Admin-Token=YOUR_TOKEN" \
  -d '{"gitUrl":"https://github.com/spring-projects/spring-boot.git"}'
```

## 预期结果

### 成功指标
- ✅ 所有页面正常加载，无404错误
- ✅ 数据库表正确创建并可以查询
- ✅ 基本的CRUD操作正常工作
- ✅ Git连接测试功能响应正常
- ✅ 扫描任务可以创建（即使暂时无法执行完整扫描）

### 可接受的限制
由于这是初始版本，以下功能可能需要进一步完善：
- 完整的Git仓库克隆和扫描流程
- CVE数据的实时同步
- 复杂的许可证检测逻辑
- 详细的扫描报告生成

## 问题排查

### 常见问题
1. **页面404错误**
   - 检查菜单配置是否正确
   - 确认Controller路径映射

2. **数据库连接错误**
   - 检查MySQL服务是否启动
   - 验证数据库连接配置

3. **权限错误**
   - 确认用户角色和权限配置
   - 检查Shiro权限注解

### 日志查看
查看应用日志以获取详细错误信息：
```bash
# 查看启动日志
tail -f logs/sys-info.log

# 查看错误日志  
tail -f logs/sys-error.log
```

## 验证完成标准

当以下条件都满足时，可以认为基本功能验证通过：
1. 应用正常启动，无严重错误
2. 安全扫描模块菜单正确显示
3. 项目管理页面可以正常访问和操作
4. 数据库表结构正确，基本数据操作正常
5. 主要API接口响应正常

这标志着RuoYi安全扫描模块的基础架构已经成功集成，可以在此基础上进行功能的进一步开发和完善。
