package com.ruoyi.security.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 许可证信息对象 license_info
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class LicenseInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 许可证ID */
    private Long licenseId;

    /** 许可证名称 */
    @Excel(name = "许可证名称")
    private String licenseName;

    /** 许可证类型 */
    @Excel(name = "许可证类型")
    private String licenseType;

    /** 风险等级(HIGH高风险 MEDIUM中风险 LOW低风险) */
    @Excel(name = "风险等级", readConverterExp = "HIGH=高风险,MEDIUM=中风险,LOW=低风险")
    private String riskLevel;

    /** 风险描述 */
    @Excel(name = "风险描述")
    private String riskDescription;

    /** 是否允许商业使用(0禁止 1允许 2未知) */
    @Excel(name = "商业使用", readConverterExp = "0=禁止,1=允许,2=未知")
    private String isCommercialUse;

    /** 是否允许修改(0禁止 1允许 2未知) */
    @Excel(name = "修改权限", readConverterExp = "0=禁止,1=允许,2=未知")
    private String isModification;

    /** 是否允许分发(0禁止 1允许 2未知) */
    @Excel(name = "分发权限", readConverterExp = "0=禁止,1=允许,2=未知")
    private String isDistribution;

    /** 许可证URL */
    @Excel(name = "许可证URL")
    private String licenseUrl;

    /** 许可证完整文本 */
    private String licenseText;

    /** 状态(0正常 1停用) */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setLicenseId(Long licenseId) 
    {
        this.licenseId = licenseId;
    }

    public Long getLicenseId() 
    {
        return licenseId;
    }

    public void setLicenseName(String licenseName) 
    {
        this.licenseName = licenseName;
    }

    public String getLicenseName() 
    {
        return licenseName;
    }

    public void setLicenseType(String licenseType) 
    {
        this.licenseType = licenseType;
    }

    public String getLicenseType() 
    {
        return licenseType;
    }

    public void setRiskLevel(String riskLevel) 
    {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel() 
    {
        return riskLevel;
    }

    public void setRiskDescription(String riskDescription) 
    {
        this.riskDescription = riskDescription;
    }

    public String getRiskDescription() 
    {
        return riskDescription;
    }

    public void setIsCommercialUse(String isCommercialUse) 
    {
        this.isCommercialUse = isCommercialUse;
    }

    public String getIsCommercialUse() 
    {
        return isCommercialUse;
    }

    public void setIsModification(String isModification) 
    {
        this.isModification = isModification;
    }

    public String getIsModification() 
    {
        return isModification;
    }

    public void setIsDistribution(String isDistribution) 
    {
        this.isDistribution = isDistribution;
    }

    public String getIsDistribution() 
    {
        return isDistribution;
    }

    public void setLicenseUrl(String licenseUrl) 
    {
        this.licenseUrl = licenseUrl;
    }

    public String getLicenseUrl() 
    {
        return licenseUrl;
    }

    public void setLicenseText(String licenseText) 
    {
        this.licenseText = licenseText;
    }

    public String getLicenseText() 
    {
        return licenseText;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("licenseId", getLicenseId())
            .append("licenseName", getLicenseName())
            .append("licenseType", getLicenseType())
            .append("riskLevel", getRiskLevel())
            .append("riskDescription", getRiskDescription())
            .append("isCommercialUse", getIsCommercialUse())
            .append("isModification", getIsModification())
            .append("isDistribution", getIsDistribution())
            .append("licenseUrl", getLicenseUrl())
            .append("licenseText", getLicenseText())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
