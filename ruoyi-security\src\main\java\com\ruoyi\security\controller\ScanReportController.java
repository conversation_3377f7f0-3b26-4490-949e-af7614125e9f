package com.ruoyi.security.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.security.domain.ScanReport;
import com.ruoyi.security.service.IScanReportService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 扫描报告Controller
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@RestController
@RequestMapping("/security/report")
public class ScanReportController extends BaseController
{
    @Autowired
    private IScanReportService scanReportService;

    /**
     * 查询扫描报告列表
     */
    @PreAuthorize("@ss.hasPermi('security:report:list')")
    @PostMapping("/list")
    public TableDataInfo list(ScanReport scanReport)
    {
        startPage();
        List<ScanReport> list = scanReportService.selectScanReportList(scanReport);
        return getDataTable(list);
    }

    /**
     * 根据项目名模糊查询报告
     */
    @PreAuthorize("@ss.hasPermi('security:report:list')")
    @GetMapping("/search")
    public TableDataInfo searchByProjectName(@RequestParam("projectName") String projectName)
    {
        startPage();
        List<ScanReport> list = scanReportService.selectScanReportByProjectName(projectName);
        return getDataTable(list);
    }

    /**
     * 导出扫描报告列表
     */
    @PreAuthorize("@ss.hasPermi('security:report:export')")
    @Log(title = "扫描报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScanReport scanReport)
    {
        List<ScanReport> list = scanReportService.selectScanReportList(scanReport);
        ExcelUtil<ScanReport> util = new ExcelUtil<ScanReport>(ScanReport.class);
        util.exportExcel(response, list, "扫描报告数据");
    }

    /**
     * 获取扫描报告详细信息
     */
    @PreAuthorize("@ss.hasPermi('security:report:query')")
    @GetMapping(value = "/{reportId}")
    public AjaxResult getInfo(@PathVariable("reportId") Long reportId)
    {
        ScanReport report = scanReportService.selectScanReportByReportId(reportId);
        if (report != null)
        {
            return success(report);
        }
        return error("扫描报告不存在");
    }

    /**
     * 获取报告内容详情
     */
    @PreAuthorize("@ss.hasPermi('security:report:query')")
    @GetMapping(value = "/content/{reportId}")
    public AjaxResult getReportContent(@PathVariable("reportId") Long reportId)
    {
        ScanReport report = scanReportService.selectScanReportByReportId(reportId);
        if (report != null)
        {
            // 返回报告内容JSON
            return success("获取成功", report.getReportContent());
        }
        return error("扫描报告不存在");
    }

    /**
     * 删除扫描报告
     */
    @PreAuthorize("@ss.hasPermi('security:report:remove')")
    @Log(title = "扫描报告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{reportIds}")
    public AjaxResult remove(@PathVariable Long[] reportIds)
    {
        return toAjax(scanReportService.deleteScanReportByReportIds(reportIds));
    }

    /**
     * 获取报告统计信息
     */
    @PreAuthorize("@ss.hasPermi('security:report:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        // 查询所有报告
        List<ScanReport> allReports = scanReportService.selectScanReportList(new ScanReport());
        
        // 统计信息
        long totalReports = allReports.size();
        long totalDependencies = allReports.stream()
            .mapToLong(report -> report.getTotalDependencies() != null ? report.getTotalDependencies() : 0)
            .sum();
        long totalCveCount = allReports.stream()
            .mapToLong(report -> report.getCveCount() != null ? report.getCveCount() : 0)
            .sum();
        long totalHighRiskLicenseCount = allReports.stream()
            .mapToLong(report -> report.getHighRiskLicenseCount() != null ? report.getHighRiskLicenseCount() : 0)
            .sum();
        
        // 构建统计结果
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();
        statistics.put("totalReports", totalReports);
        statistics.put("totalDependencies", totalDependencies);
        statistics.put("totalCveCount", totalCveCount);
        statistics.put("totalHighRiskLicenseCount", totalHighRiskLicenseCount);
        
        return success(statistics);
    }

    /**
     * 获取最近的扫描报告
     */
    @PreAuthorize("@ss.hasPermi('security:report:list')")
    @GetMapping("/recent")
    public AjaxResult getRecentReports(@RequestParam(value = "limit", defaultValue = "10") int limit)
    {
        ScanReport queryParam = new ScanReport();
        // 这里可以添加排序逻辑，按创建时间倒序
        List<ScanReport> list = scanReportService.selectScanReportList(queryParam);
        
        // 限制返回数量
        if (list.size() > limit)
        {
            list = list.subList(0, limit);
        }
        
        return success(list);
    }
}
