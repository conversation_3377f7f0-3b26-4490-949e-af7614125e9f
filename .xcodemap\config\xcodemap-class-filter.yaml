autoDetectedPackages:
- com.ruoyi.common.annotation
- com.ruoyi.common.config
- com.ruoyi.common.constant
- com.ruoyi.common.core
- com.ruoyi.common.enums
- com.ruoyi.common.exception
- com.ruoyi.common.json
- com.ruoyi.common.utils
- com.ruoyi.common.xss
- com.ruoyi.framework.aspectj
- com.ruoyi.framework.config
- com.ruoyi.framework.datasource
- com.ruoyi.framework.interceptor
- com.ruoyi.framework.manager
- com.ruoyi.framework.shiro
- com.ruoyi.framework.web
- com.ruoyi.generator.config
- com.ruoyi.generator.controller
- com.ruoyi.generator.domain
- com.ruoyi.generator.mapper
- com.ruoyi.generator.service
- com.ruoyi.generator.util
- com.ruoyi.quartz.config
- com.ruoyi.quartz.controller
- com.ruoyi.quartz.domain
- com.ruoyi.quartz.mapper
- com.ruoyi.quartz.service
- com.ruoyi.quartz.task
- com.ruoyi.quartz.util
- com.ruoyi.security
- com.ruoyi.system.domain
- com.ruoyi.system.mapper
- com.ruoyi.system.service
enableAutoDetect: true
entryDisplayConfig:
  excludedPathPatterns: []
  skipJsCss: true
funcDisplayConfig:
  skipConstructors: false
  skipFieldAccess: true
  skipFieldChange: true
  skipGetters: false
  skipNonProjectPackages: false
  skipPrivateMethods: false
  skipSetters: false
ignoreSameClassCall: null
ignoreSamePackageCall: null
includedPackagePrefixes: null
includedParentClasses: null
name: xcodemap-filter
recordMode: all
sourceDisplayConfig:
  color: blue
startOnDebug: false
