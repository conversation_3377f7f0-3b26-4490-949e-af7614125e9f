# 测试数据库配置说明

## 概述

本项目的测试环境使用MySQL数据库，需要在运行测试前进行相应的数据库配置。

## 数据库配置要求

### 1. MySQL服务器要求
- MySQL版本：5.7或更高版本
- 字符集：utf8mb4
- 排序规则：utf8mb4_general_ci

### 2. 数据库连接配置
测试环境使用以下数据库连接配置：
- 主机：localhost
- 端口：3306
- 数据库名：ry_test
- 用户名：root
- 密码：123456

## 数据库初始化步骤

### 步骤1：创建测试数据库
在MySQL中执行以下命令创建测试数据库：

```sql
CREATE DATABASE IF NOT EXISTS ry_test DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

### 步骤2：执行初始化脚本
运行数据库初始化脚本创建表结构和基础数据：

```bash
mysql -u root -p123456 < src/test/resources/sql/init-test-database.sql
```

或者在MySQL客户端中执行：
```sql
SOURCE src/test/resources/sql/init-test-database.sql;
```

### 步骤3：插入测试数据（可选）
如果需要预置测试数据，可以执行：

```bash
mysql -u root -p123456 ry_test < src/test/resources/sql/test-data.sql
```

## 表结构说明

测试数据库包含以下核心表：

### 1. 系统字典表
- `sys_dict_type`：字典类型表
- `sys_dict_data`：字典数据表

### 2. 业务核心表
- `project_info`：项目信息表
- `scan_task`：扫描任务表
- `cve_info`：CVE信息表
- `project_dependency`：项目依赖表
- `scan_report`：扫描报告表

## 测试配置文件

测试环境的数据库配置位于：
- `src/test/resources/application-test.yml`

主要配置项：
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      master:
        url: ************************************************************************************************************************************************
        username: root
        password: 123456
```

## 运行测试

### 1. 确保数据库服务运行
```bash
# 检查MySQL服务状态
systemctl status mysql
# 或
service mysql status
```

### 2. 验证数据库连接
```bash
mysql -u root -p123456 -e "USE ry_test; SHOW TABLES;"
```

### 3. 运行测试用例
```bash
# 运行所有测试
mvn test -Dspring.profiles.active=test

# 运行特定测试类
mvn test -Dtest=DatabaseVerificationTest -Dspring.profiles.active=test
```

## 常见问题解决

### 1. 连接被拒绝
- 检查MySQL服务是否启动
- 验证端口3306是否开放
- 确认用户名密码正确

### 2. 数据库不存在
- 手动创建ry_test数据库
- 执行初始化脚本

### 3. 权限问题
- 确保root用户有足够权限
- 或创建专用测试用户：
```sql
CREATE USER 'test_user'@'localhost' IDENTIFIED BY 'test_password';
GRANT ALL PRIVILEGES ON ry_test.* TO 'test_user'@'localhost';
FLUSH PRIVILEGES;
```

### 4. 字符集问题
- 确保数据库使用utf8mb4字符集
- 检查MySQL配置文件中的字符集设置

## 清理测试数据

测试完成后，可以清理测试数据：

```sql
-- 清理测试数据但保留表结构
DELETE FROM scan_report WHERE project_name LIKE '测试%';
DELETE FROM project_dependency WHERE task_id IN (SELECT task_id FROM scan_task WHERE task_name LIKE '测试%');
DELETE FROM scan_task WHERE task_name LIKE '测试%';
DELETE FROM project_info WHERE project_name LIKE '测试%';
DELETE FROM cve_info WHERE cve_id LIKE 'CVE-TEST%';

-- 或完全删除测试数据库
DROP DATABASE IF EXISTS ry_test;
```

## 注意事项

1. **数据隔离**：测试数据库与生产数据库完全隔离，避免测试影响生产数据
2. **事务回滚**：测试用例使用`@Transactional`注解，确保测试数据自动回滚
3. **并发测试**：多个测试可能并发执行，注意数据冲突
4. **性能考虑**：测试数据库连接池配置较小，适合测试环境使用

## 持续集成配置

在CI/CD环境中，可以使用以下方式自动化数据库配置：

```yaml
# GitHub Actions示例
- name: Setup MySQL
  uses: mirromutth/mysql-action@v1.1
  with:
    mysql version: '8.0'
    mysql database: 'ry_test'
    mysql root password: '123456'

- name: Initialize Database
  run: |
    mysql -h127.0.0.1 -uroot -p123456 < src/test/resources/sql/init-test-database.sql
```

## 联系方式

如有数据库配置相关问题，请联系开发团队或查看项目文档。
