package com.ruoyi.security.integration;

import com.ruoyi.security.domain.ProjectInfo;
import com.ruoyi.security.domain.ScanTask;
import com.ruoyi.security.domain.ScanReport;
import com.ruoyi.security.domain.ProjectDependency;
import com.ruoyi.security.service.IProjectInfoService;
import com.ruoyi.security.service.IScanService;
import com.ruoyi.security.service.IScanTaskService;
import com.ruoyi.security.service.IScanReportService;
import com.ruoyi.security.service.IProjectDependencyService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 安全扫描集成测试
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class SecurityScanIntegrationTest {

    @Autowired
    private IProjectInfoService projectInfoService;

    @Autowired
    private IScanService scanService;

    @Autowired
    private IScanTaskService scanTaskService;

    @Autowired
    private IScanReportService scanReportService;

    @Autowired
    private IProjectDependencyService projectDependencyService;

    private ProjectInfo testProject;

    @BeforeEach
    void setUp() {
        // 准备测试项目
        testProject = new ProjectInfo();
        testProject.setProjectName("集成测试项目");
        testProject.setProjectCode("INTEGRATION001");
        testProject.setGitUrl("https://github.com/octocat/Hello-World.git");
        testProject.setGitBranch("master");
        testProject.setProjectDesc("用于集成测试的项目");
        testProject.setProjectOwner("admin");
        testProject.setStatus("0");
    }

    @Test
    @DisplayName("测试完整的扫描流程")
    void testCompleteScanWorkflow() throws InterruptedException {
        // 1. 创建项目
        int result = projectInfoService.insertProjectInfo(testProject);
        assertEquals(1, result, "项目创建应该成功");
        assertNotNull(testProject.getProjectId(), "项目ID应该自动生成");

        // 2. 验证项目创建
        ProjectInfo createdProject = projectInfoService.selectProjectInfoByProjectId(testProject.getProjectId());
        assertNotNull(createdProject, "应该能查询到创建的项目");
        assertEquals("INTEGRATION001", createdProject.getProjectCode(), "项目编码应该匹配");

        // 3. 执行扫描（注意：这里使用模拟扫描，避免真实的Git操作）
        Long taskId = scanService.executeScan(testProject.getProjectId());
        assertNotNull(taskId, "扫描任务应该创建成功");

        // 4. 验证扫描任务创建
        ScanTask scanTask = scanTaskService.selectScanTaskByTaskId(taskId);
        assertNotNull(scanTask, "应该能查询到扫描任务");
        assertEquals(testProject.getProjectId(), scanTask.getProjectId(), "任务项目ID应该匹配");

        // 5. 等待扫描完成（模拟异步处理）
        // 在实际测试中，这里可能需要等待或使用Mock
        TimeUnit.SECONDS.sleep(2);

        // 6. 检查扫描状态
        ScanTask updatedTask = scanTaskService.selectScanTaskByTaskId(taskId);
        assertNotNull(updatedTask, "应该能查询到更新的任务");
        // 任务状态可能是完成或失败，取决于网络环境
        assertTrue("2".equals(updatedTask.getTaskStatus()) || "3".equals(updatedTask.getTaskStatus()),
                  "任务应该是完成或失败状态");

        // 7. 如果扫描成功，验证依赖数据
        if ("2".equals(updatedTask.getTaskStatus())) {
            List<ProjectDependency> dependencies = projectDependencyService.selectProjectDependencyByTaskId(taskId);
            assertNotNull(dependencies, "依赖列表不应该为null");
            // 依赖数量取决于实际项目，这里只验证数据结构
            
            // 8. 验证扫描报告
            ScanReport queryParam = new ScanReport();
            queryParam.setTaskId(taskId);
            List<ScanReport> reports = scanReportService.selectScanReportList(queryParam);
            assertNotNull(reports, "报告列表不应该为null");
            
            if (!reports.isEmpty()) {
                ScanReport report = reports.get(0);
                assertEquals(testProject.getProjectId(), report.getProjectId(), "报告项目ID应该匹配");
                assertNotNull(report.getReportContent(), "报告内容不应该为空");
            }
        }
    }

    @Test
    @DisplayName("测试项目管理完整流程")
    void testProjectManagementWorkflow() {
        // 1. 创建项目
        int result = projectInfoService.insertProjectInfo(testProject);
        assertEquals(1, result, "项目创建应该成功");

        // 2. 查询项目列表
        ProjectInfo queryParam = new ProjectInfo();
        queryParam.setProjectName("集成测试");
        List<ProjectInfo> projects = projectInfoService.selectProjectInfoList(queryParam);
        assertFalse(projects.isEmpty(), "应该能查询到项目");
        assertTrue(projects.stream().anyMatch(p -> "INTEGRATION001".equals(p.getProjectCode())),
                  "列表中应该包含测试项目");

        // 3. 更新项目
        testProject.setProjectDesc("更新后的项目描述");
        testProject.setScanStatus("1");
        result = projectInfoService.updateProjectInfo(testProject);
        assertEquals(1, result, "项目更新应该成功");

        // 4. 验证更新
        ProjectInfo updatedProject = projectInfoService.selectProjectInfoByProjectId(testProject.getProjectId());
        assertEquals("更新后的项目描述", updatedProject.getProjectDesc(), "项目描述应该已更新");
        assertEquals("1", updatedProject.getScanStatus(), "扫描状态应该已更新");

        // 5. 测试项目编码唯一性
        ProjectInfo duplicateProject = new ProjectInfo();
        duplicateProject.setProjectCode("INTEGRATION001");
        String uniqueResult = projectInfoService.checkProjectCodeUnique(duplicateProject);
        assertEquals("1", uniqueResult, "重复编码应该返回不唯一");

        // 6. 测试新编码唯一性
        ProjectInfo newProject = new ProjectInfo();
        newProject.setProjectCode("NEW_CODE");
        uniqueResult = projectInfoService.checkProjectCodeUnique(newProject);
        assertEquals("0", uniqueResult, "新编码应该返回唯一");

        // 7. 删除项目
        result = projectInfoService.deleteProjectInfoByProjectId(testProject.getProjectId());
        assertEquals(1, result, "项目删除应该成功");

        // 8. 验证软删除
        ProjectInfo deletedProject = projectInfoService.selectProjectInfoByProjectId(testProject.getProjectId());
        assertNull(deletedProject, "删除后应该查询不到项目");
    }

    @Test
    @DisplayName("测试扫描任务管理")
    void testScanTaskManagement() {
        // 1. 先创建项目
        projectInfoService.insertProjectInfo(testProject);

        // 2. 创建扫描任务
        ScanTask scanTask = new ScanTask();
        scanTask.setProjectId(testProject.getProjectId());
        scanTask.setTaskName("集成测试扫描任务");
        scanTask.setTaskStatus("0");
        
        int result = scanTaskService.insertScanTask(scanTask);
        assertEquals(1, result, "扫描任务创建应该成功");
        assertNotNull(scanTask.getTaskId(), "任务ID应该自动生成");

        // 3. 查询扫描任务
        ScanTask createdTask = scanTaskService.selectScanTaskByTaskId(scanTask.getTaskId());
        assertNotNull(createdTask, "应该能查询到扫描任务");
        assertEquals("集成测试扫描任务", createdTask.getTaskName(), "任务名称应该匹配");

        // 4. 更新扫描任务状态
        scanTask.setTaskStatus("1");
        scanTask.setStartTime(new java.util.Date());
        result = scanTaskService.updateScanTask(scanTask);
        assertEquals(1, result, "任务更新应该成功");

        // 5. 验证更新
        ScanTask updatedTask = scanTaskService.selectScanTaskByTaskId(scanTask.getTaskId());
        assertEquals("1", updatedTask.getTaskStatus(), "任务状态应该已更新");
        assertNotNull(updatedTask.getStartTime(), "开始时间应该已设置");

        // 6. 查询任务列表
        ScanTask queryParam = new ScanTask();
        queryParam.setProjectId(testProject.getProjectId());
        List<ScanTask> tasks = scanTaskService.selectScanTaskList(queryParam);
        assertFalse(tasks.isEmpty(), "应该能查询到任务列表");
        assertTrue(tasks.stream().anyMatch(t -> scanTask.getTaskId().equals(t.getTaskId())),
                  "列表中应该包含测试任务");

        // 7. 删除扫描任务
        result = scanTaskService.deleteScanTaskByTaskId(scanTask.getTaskId());
        assertEquals(1, result, "任务删除应该成功");
    }

    @Test
    @DisplayName("测试依赖数据管理")
    void testDependencyDataManagement() {
        // 1. 先创建项目和任务
        projectInfoService.insertProjectInfo(testProject);
        
        ScanTask scanTask = new ScanTask();
        scanTask.setProjectId(testProject.getProjectId());
        scanTask.setTaskName("依赖测试任务");
        scanTaskService.insertScanTask(scanTask);

        // 2. 创建测试依赖
        ProjectDependency dependency1 = createTestDependency(scanTask.getTaskId(), 
                                                           "org.apache.commons", "commons-lang3", "3.12.0");
        ProjectDependency dependency2 = createTestDependency(scanTask.getTaskId(), 
                                                           "junit", "junit", "4.13.2");

        // 3. 批量插入依赖
        List<ProjectDependency> dependencies = List.of(dependency1, dependency2);
        int result = projectDependencyService.batchInsertProjectDependency(dependencies);
        assertEquals(2, result, "应该成功插入2个依赖");

        // 4. 查询依赖列表
        List<ProjectDependency> savedDependencies = 
            projectDependencyService.selectProjectDependencyByTaskId(scanTask.getTaskId());
        assertEquals(2, savedDependencies.size(), "应该查询到2个依赖");

        // 5. 验证依赖数据
        ProjectDependency firstDep = savedDependencies.get(0);
        assertNotNull(firstDep.getDependencyId(), "依赖ID应该自动生成");
        assertNotNull(firstDep.getGroupId(), "GroupId不应该为空");
        assertNotNull(firstDep.getArtifactId(), "ArtifactId不应该为空");
        assertNotNull(firstDep.getVersion(), "Version不应该为空");

        // 6. 更新依赖信息
        firstDep.setHasCve("1");
        firstDep.setCveCount(2L);
        firstDep.setMaxCvssScore(java.math.BigDecimal.valueOf(7.5));
        result = projectDependencyService.updateProjectDependency(firstDep);
        assertEquals(1, result, "依赖更新应该成功");

        // 7. 验证更新
        ProjectDependency updatedDep = 
            projectDependencyService.selectProjectDependencyByDependencyId(firstDep.getDependencyId());
        assertEquals("1", updatedDep.getHasCve(), "CVE标志应该已更新");
        assertEquals(2L, updatedDep.getCveCount(), "CVE数量应该已更新");
    }

    /**
     * 创建测试依赖对象
     */
    private ProjectDependency createTestDependency(Long taskId, String groupId, String artifactId, String version) {
        ProjectDependency dependency = new ProjectDependency();
        dependency.setTaskId(taskId);
        dependency.setGroupId(groupId);
        dependency.setArtifactId(artifactId);
        dependency.setVersion(version);
        dependency.setScope("compile");
        dependency.setHasCve("0");
        dependency.setCveCount(0L);
        dependency.setLicenseRiskLevel("1");
        return dependency;
    }

    @Test
    @DisplayName("测试数据一致性")
    void testDataConsistency() {
        // 1. 创建完整的数据链
        projectInfoService.insertProjectInfo(testProject);
        
        ScanTask scanTask = new ScanTask();
        scanTask.setProjectId(testProject.getProjectId());
        scanTask.setTaskName("一致性测试任务");
        scanTaskService.insertScanTask(scanTask);

        ProjectDependency dependency = createTestDependency(scanTask.getTaskId(), 
                                                          "test.group", "test-artifact", "1.0.0");
        projectDependencyService.insertProjectDependency(dependency);

        // 2. 验证数据关联
        ScanTask savedTask = scanTaskService.selectScanTaskByTaskId(scanTask.getTaskId());
        assertEquals(testProject.getProjectId(), savedTask.getProjectId(), "任务项目ID应该匹配");

        List<ProjectDependency> taskDependencies = 
            projectDependencyService.selectProjectDependencyByTaskId(scanTask.getTaskId());
        assertFalse(taskDependencies.isEmpty(), "任务应该有关联的依赖");
        assertEquals(scanTask.getTaskId(), taskDependencies.get(0).getTaskId(), "依赖任务ID应该匹配");

        // 3. 测试级联查询
        ProjectInfo queryParam = new ProjectInfo();
        queryParam.setProjectId(testProject.getProjectId());
        List<ProjectInfo> projects = projectInfoService.selectProjectInfoList(queryParam);
        assertFalse(projects.isEmpty(), "应该能查询到项目");

        // 4. 验证软删除不影响数据一致性
        projectInfoService.deleteProjectInfoByProjectId(testProject.getProjectId());
        
        // 项目删除后，任务和依赖仍然存在（业务规则决定）
        ScanTask taskAfterProjectDelete = scanTaskService.selectScanTaskByTaskId(scanTask.getTaskId());
        assertNotNull(taskAfterProjectDelete, "项目删除后任务仍应存在");
    }
}
